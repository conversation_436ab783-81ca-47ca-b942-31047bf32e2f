
#-----------------------------------------------------------------
# Created by: <PERSON>, 2022.04.15
#   Used for: replace strings in the commands by integer numbers,
#             so as to simplify the msg.
#             This file is in accordance with 'public_blr.hpp'
#-----------------------------------------------------------------

from enum import Enum, unique
from rospkg import on_ros_path
import yaml, io                                         # for loading parameters from yaml file

from print_type  import PRINT_TYPE as pt


#--- This enumerator lists all steps how to lay bricks.
#--- As always, the command msg goes as following:
#--- [STEP][parameter(s)]
#--- STEP: 		the integer value listed in this enum.
#--- values:	position/speed/pose/distance/amplitude/etc
@unique
class STEP(Enum):
    null               = 0
    stop               = 1	   # stop all motions and steps
    pause		       = 2	   # stop current control step
    unpause		       = 3	   # continue: restart current control step
    ready              = 4	   # reserved
    loadParam          = 5     # load parameters from yaml file

    stepback    	   = 10    # robot tracks laser and move backward; paver leveling
    scrape      	   = 11    # (unused) robot stands still and use paver to scrape sands
    shotcreting 	   = 12    # (unused) shotcreting using a 3-DOF arm after sand scraping
    fetch       	   = 13    # grasp brick(s), lift them up when grasped
    daub        	   = 14    # daub mud onto brick using robot arm
    moveRight   	   = 15    # robot arm moves to a front-right place, including workend leveling
    moveMiddle  	   = 16    # robot arm moves to a front-middle place, including workend leveling
    moveLeft    	   = 17    # robot arm moves to a front-left place, including workend leveling
    moveLeft2    	   = 18    # robot arm moves to a front-left2 place, including workend leveling
    locaGlobal  	   = 19    # (unused) use the center camera to localize pose of workend
    moveAdjGlobal	   = 20	   # (unused) adjustment according to global positioning
    descend     	   = 21    # robot arm moves down to approach the place the brick to be laid
    locaFine    	   = 22    # (unused) use the side camera to localize pos of workend
    moveAdjFineAngle   = 23	   # (unused) adjust angle of workend according to remote controller or CV positioning
    moveAdjFinePos     = 24	   # (unused) adjust position of workend according to remote controller or CV positioning
    moveAdjHuman       = 25	   # adjust angle or position of workend by human intervention
    lay                = 26    # lay the brick to the proper place
    flatten            = 27    # new: flaten the laid tile (split from lay)
    moveRear 		   = 28    # robot arm moves to the rear of the robot (above the bricks) from middle/left/right
    moveFront		   = 29    # (new) robot arm moves to the front (left/middle/right) in continous motion mode, including daubing
    moveRear2		   = 30    # (new) robot arm moves to the rear in continous motion mode
    locaAdjust         = 31    # (new) use new method for localization
	
    macroAll           = 50    # perform all steps of laying bricks
    macroMiddle	       = 51	   # execute a serial of steps to lay tiles on the midlle
    macroRight	       = 52	   # execute a serial of steps to lay tiles on the right side
    macroLeft          = 53	   # execute a serial of steps to lay tiles on the left side

    acqPosArm	       = 60	   # acquire positions, i.e. XYZU and J1-J4 and outer axisL roll and pitch
    acqAxisBase		   = 61    # acquire positions of base axises
    acqPosBase         = 62	   # acquire base's pose, i.e. biasX, biasY, biasA according to PSD measurement

    testLinearAbsCtl   = 90	   # linear control function in abs. mode
    testLinearIncCtl   = 91    # linear control function in inc. mode
    testMyKineCtrl	   = 92    # test my kinematice mode based linear control function
    testContinuousManip   = 93
    testContiManipStop    = 94
    testContiManipSuspend = 95
    testContiManipStart   = 96
    testContinuousManipBack   = 97

    end                = 100


def TransEnumStep(index):
	if(index == STEP.null.value):					return "STEP-null"
	elif(index == STEP.stop.value):					return "STEP-stop"
	elif(index == STEP.pause.value):				return "STEP-pause"
	elif(index == STEP.unpause.value):				return "STEP-unpause(continue)"
	elif(index == STEP.ready.value):				return "STEP-ready"
	elif(index == STEP.loadParam.value):			return "STEP-load-params-from-yaml"

	elif(index == STEP.stepback.value):				return "STEP-stepback"
	elif(index == STEP.scrape.value):				return "STEP-scrape"
	elif(index == STEP.shotcreting.value):			return "STEP-shotcreting"
	elif(index == STEP.fetch.value):				return "STEP-fetch-bricks"
	elif(index == STEP.daub.value):					return "STEP-daub-mud-on-bricks"
	elif(index == STEP.moveMiddle.value):			return "STEP-move-arm-to-middle"
	elif(index == STEP.moveLeft.value):				return "STEP-move-arm-to-left"
	elif(index == STEP.moveRight.value):			return "STEP-move-arm-to-right"
	elif(index == STEP.locaGlobal.value):			return "STEP-global-image-positioning"
	elif(index == STEP.moveAdjGlobal.value):		return "STEP-move-arm-adj-by-global-CV"
	elif(index == STEP.descend.value):				return "STEP-move-arm-descend"
	elif(index == STEP.locaFine.value):				return "STEP-local-image-positioning"
	elif(index == STEP.moveAdjFineAngle.value):		return "STEP-adj-angle-by-fine-CV"
	elif(index == STEP.moveAdjFinePos.value):		return "STEP-adj-pos-by-fine-CV"
	elif(index == STEP.moveAdjHuman.value):			return "STEP-adj-pos-by-human"
	elif(index == STEP.lay.value):					return "STEP-lay-bricks"
	elif(index == STEP.flatten.value):				return "STEP-flatten-bricks(new)"
	elif(index == STEP.moveRear.value):				return "STEP-move-arm-rear"
	elif(index == STEP.moveRear2.value):			return "STEP-move-arm-rear(new)"
	elif(index == STEP.moveFront.value):			return "STEP-move-arm-front(new)"
	elif(index == STEP.locaAdjust.value):			return "STEP-loca-and-adjust(new)"

	elif(index == STEP.macroAll.value):				return "STEP-all-macro-steps"
	elif(index == STEP.macroMiddle.value):			return "STEP-middle-side-tiling-steps"
	elif(index == STEP.macroLeft.value):			return "STEP-left-side-tiling-steps"
	elif(index == STEP.macroRight.value):			return "STEP-right-side-tiling-steps"

	elif(index == STEP.acqPosArm.value):			return "STEP-acquire-arm-pos"
	elif(index == STEP.acqAxisBase.value):			return "STEP-acquire-base-motors-pos"
	elif(index == STEP.acqPosBase.value):			return "STEP-acquire-base-pos"

	elif(index == STEP.testLinearAbsCtl.value):		return "STEP-test-abs-linear-ctl"
	elif(index == STEP.testLinearIncCtl.value):		return "STEP-test-inc-linear-ctl"
	elif(index == STEP.testMyKineCtrl.value):		return "STEP-test-my-kine-inc-linear-ctl"
	elif(index == STEP.testContinuousManip.value):	return "STEP-test-continuous-motion-control"
	elif(index == STEP.testContiManipStop.value):	return "STEP-test-continuous-motion-stop"
	elif(index == STEP.testContiManipSuspend.value):return "STEP-test-continuous-motion-suspend"
	elif(index == STEP.testContiManipStart.value):	return "STEP-test-continuous-motion-start"
	elif(index == STEP.testContinuousManipBack.value):	return "STEP-test-continuous-motion-backward"

	elif(index == STEP.end.value):					return "STEP-end(meaningless)"
	else:
		return "STEP-error: step-mismatch"


@unique
class MOD(Enum):
	inc      = 0		# incremental position control mode
	abs      = 1		# absolute position control mode

@unique
class RET(Enum):
	null        = 0
	normal      = 1		# normal return
	timeout     = 2		# judge as timeout
	cancel		= 3		# judge as preempt (goal canceled in action)


#--- This enumerator lists all commands of controlling the brick-laying robot.
#--- As always, the command msg goes as following:
#--- [CMD][state][values]
#--- CMD: 		the integer value listed in this enum.
#--- state: 	0-stop, 1~N-turn on specific
#--- values:	position/speed/pose/distance/amplitude/etc
@unique
class CMD(Enum):
	null 		    = 0
	allStop	        = 1		# stop all motion control
	help			= 2	    # remote controll by hand
	clearAlarm  	= 3		# clear alarms for Zmotion controller
	axisControl     = 4     # control an assigned axis in a general way

	#--- main arm control (migrated to ZMotion)
	armJoint        = 5     # main arm joint control
	armLinear       = 6     # main arm linear control
	armStop         = 7     # stop main arm motion

	#--- vehicle base
	supportLift		= 10    # support the platform, lift vehicle base above the ground, followed by state (0-stop, 1-move) and height value
	supportLeveling	= 11    # leveling the platform
	steerTrans		= 12	# steering all four wheels to the same orientation for tranlational move
	steerTurn		= 13	# steering four wheels with different angles for head-turning move
	spin			= 14	# steering on-site with zero-steering radius
	wheelRoll		= 15 	# let wheels roll so that the robot moves forward or backward
	trackLine		= 16	# move along the laser beam via dynamic control
	moveLat         = 17    # move the robot in lateral direction without turning its head
	tileAlign   	= 18    # actuate two motors to align tiles

	#--- paver
	paverLift		= 20	# move paver to certain height, followed by state (0-stop, 1-perform) and height value
	paverRoll		= 21	# roll angle of paver, followed by state (0-stop, 1-perform) and angle value
	paverDeliver	= 22	# turn on sand delivering mechanism, followed by state (0-stop, 1-perform) and speed value
	paverLeveling	= 23	# leveling and height adjustment according to laser beam, followed by state (0-stop, 1-perform)

	#--- workend
	suck	    	 = 30	# turn on sucker or not, which is determined by followed state (0-stop, 1-perform)
	vibrate			 = 31   # turn on vibrate motor or not, followed by state (0-stop, 1-perform) and amplitude value
	lamp			 = 32	# turn on the lamp or shut it down
	workendLeveling  = 33	# trigger or stop leveling of the workend, folloed by state (0-stop, 1-perform)
	workendJoints    = 34
	workendAlignTilt = 35	# make the workend a pose parallel to the tiles for easier fetching
	workendTrans     = 36   # use workend translational mechanism to adjust CV bias

	#--- positioning
	scanLoca        = 40	# perform height scanning, and calculate angular and position bias
	moveScanner     = 41
	scanLoca2       = 42	# perform calculation of angular and position bias based on scanner products

	#--- acquisition
	axisPosition 	= 50	# get position of Zmotion motor
	axisTorque      = 51	# get torque of motors
	axisPosBase	    = 52	# get position of Zmotion motor
	laserDist		= 53	# get laser distance
	pressure    	= 54	# get air pressure of sucker
	psd             = 55	# get PSD positioning data
	basePose        = 56	# get pose of robot's base by PSD data
	restNodes       = 57    # get rest nodes while in continuous motion using zMotion

	connframe   = 60	# use zmotion to manipulate the main arm, instead of using iNexBot controller
	mainarmOp   = 61	# pause or resume the motion of the main arm
	mainarmPos  = 62	# use zmotion to manipulate the main arm, instead of using iNexBot controller, POSE mode
	mainarmVel  = 63	# use zmotion to manipulate the main arm, instead of using iNexBot controller, VELOCITY mode

	#--- test
	tmp             = 100

	end = 150
 

def TransEnumCmd(index):
	if(index == CMD.null.value):				return "CMD-null"
	elif(index == CMD.allStop.value):			return "CMD-all-stop"
	elif(index == CMD.help.value):				return "CMD-remote-control"
	elif(index == CMD.armJoint.value):			return "CMD-arm-joint-control"
	elif(index == CMD.armLinear.value):			return "CMD-arm-linear-control"
	elif(index == CMD.armStop.value):			return "CMD-arm-stop"
	elif(index == CMD.supportLift.value):		return "CMD-support-lift"
	elif(index == CMD.supportLeveling.value):	return "CMD-support-leveling"
	elif(index == CMD.steerTrans.value):		return "CMD-steer-same-angle-for-four-wheels"
	elif(index == CMD.steerTurn.value):			return "CMD-steer-diff-angle-for-four-wheels"
	elif(index == CMD.spin.value):				return "CMD-spin"
	elif(index == CMD.wheelRoll.value):			return "CMD-wheel-roll"
	elif(index == CMD.trackLine.value):			return "CMD-track-line"
	elif(index == CMD.paverLift.value):			return "CMD-paver-lift"
	elif(index == CMD.paverRoll.value):			return "CMD-paver-roll"
	elif(index == CMD.paverDeliver.value):		return "CMD-paver-deliver"
	elif(index == CMD.paverLeveling.value):		return "CMD-paver-leveling"
	elif(index == CMD.suck.value):				return "CMD-vacuum"
	elif(index == CMD.vibrate.value):			return "CMD-vibrate"
	elif(index == CMD.lamp.value):				return "CMD-lamp"
	elif(index == CMD.workendLeveling.value):	return "CMD-workend-leveling"
	elif(index == CMD.workendAlignTilt.value):	return "CMD-workend-tilt-alignment"
	elif(index == CMD.workendJoints.value):		return "CMD-workend-motors"
	elif(index == CMD.workendTrans.value):		return "CMD-workend-trans-move"
	elif(index == CMD.scanLoca.value):			return "CMD-loca-via-scan_modules"
	elif(index == CMD.scanLoca2.value):			return "CMD-loca-via-scanners"
	elif(index == CMD.moveScanner.value):		return "CMD-move-scanner-frame"
	elif(index == CMD.axisPosBase.value):		return "CMD-acq-base-pose"
	elif(index == CMD.laserDist.value):			return "CMD-acq-laser-dist"
	elif(index == CMD.pressure.value):			return "CMD-acq-pressure"
	elif(index == CMD.psd.value):				return "CMD-acq-psd"
	elif(index == CMD.basePose.value):			return "CMD-base-pose"
	elif(index == CMD.restNodes.value):			return "CMD-acq-rest-nodes"
	elif(index == CMD.connframe.value):			return "CMD-construct-frame5"
	elif(index == CMD.mainarmOp.value):			return "CMD-zMotion-continuous-pause-or-continue"
	elif(index == CMD.mainarmPos.value):		return "CMD-zMotion-continuous-goal-pose-mode"
	elif(index == CMD.mainarmVel.value):		return "CMD-zMotion-continuous-speed-mode"

	elif(index == CMD.tmp.value):				return "CMD-temp"
	elif(index == CMD.end.value):				return "CMD-end"
	else:
		return "CMD-error-mismatch:" + str(index)



@unique
class REPLY(Enum):
	null          = 0
	received	  = 1
	executed      = 2
	preempted     = 3		# update params. of new received command and execute it right now
	rejected	  = 4	    # illegal command received, refuse to exexute
	busy		  = 5	    # last same task is executing currently, ignore this new command
	finished      = 6		# task is over, but without info. of successfully done or failed
	succeeded     = 7
	timedue       = 8		# send reply by time counting (need to be further replaced by actual feedback info.)
	terminated    = 9		# normally stop the motion control according to cmd.
	failed        = 10
	aborted 	  = 11		# finish task with errors
	timeout		  = 12
	exception     = 13	    # exceptions like no data back after acquisition cmd.'s sending
	running       = 14      # specifically for continuous motion control using zMotion controller


def TransEnumReply(index):
	if(index == REPLY.null.value):			return "REPLY-null"
	elif(index == REPLY.received.value):	return "REPLY-received"
	elif(index == REPLY.executed.value):	return "REPLY-executed"
	elif(index == REPLY.preempted.value):	return "REPLY-preempted"
	elif(index == REPLY.rejected.value):	return "REPLY-rejected"
	elif(index == REPLY.busy.value):		return "REPLY-busy"
	elif(index == REPLY.finished.value):	return "REPLY-finished"
	elif(index == REPLY.succeeded.value):	return "REPLY-succeeded"
	elif(index == REPLY.timedue.value):		return "REPLY-timedue"
	elif(index == REPLY.terminated.value):	return "REPLY-terminated"
	elif(index == REPLY.failed.value):		return "REPLY-failed"
	elif(index == REPLY.aborted.value):		return "REPLY-aborted"
	elif(index == REPLY.timeout.value):		return "REPLY-timeout"
	elif(index == REPLY.exception.value):	return "REPLY-exceptions"
	elif(index == REPLY.running.value):		return "REPLY-running"
	else:
		return "REPLY-error-mismatch"


@unique
class ERROR(Enum):
	null = 100
	timeoutArm = 101


class SWITCH(Enum):
	off     = 0
	stop    = 0
	on      = 1
	release = 2

@unique
class LOCA(Enum):
	null  = 0
	fetch = 1
	lay   = 2

#--- for all these parameters, we use unit of mm, mm/s, ms, and mdeg as default
class PARAM():

	def __init__(self, AddrYamlFile):
		self.LoadParamsFromYaml(AddrYamlFile)

	def LoadParamsFromYaml(self, AddrYamlFile):
		param = yaml.safe_load(io.open(AddrYamlFile, "r", encoding='utf-8')) # replace function 'yaml.load(param)'

		self.globalTileLon        = param['global']['tile_size_lon'   ]
		self.globalTileLat        = param['global']['tile_size_lat'   ]
		self.globalFetchMarginLon = param['global']['fetch_margin_lon']
		self.globalFetchMarginLat = param['global']['fetch_margin_lat']
		self.globalSampleTileLon  = param['global']['sample_tile_lon' ]
		self.globalSampleTileLat  = param['global']['sample_tile_lat' ]

		self.scannerRangeMin = param['scanner']['scanner_range_min']
		self.scannerRangeMax = param['scanner']['scanner_range_max']

		self.timeoutHiNoOp           = param['timeout']['hi_no_operation'  ]
		self.timeoutHiOperate        = param['timeout']['hi_operation'     ]
		self.timeoutControlBaseReply = param['timeout']['controlbase_reply']
		self.timeoutWorkendLeveling  = param['timeout']['workend_leveling' ]
		self.timeoutPaverLeveling    = param['timeout']['paver_leveling'   ]

		self.tolDistPaverLeveling    = param['tolerance']['paver_leveling_height' ]
		self.tolAngleWorkendLeveling = param['tolerance']['workend_leveling_angle']
		self.tolAngleTiltAlign       = param['tolerance']['fetch_tilt_align_angle']
		
		#--- continuous motion control: [X,Y,Z,U,V,Coord]
		self.waypointInitPos   = param['arm_waypoints']['init_pos'  ][0:4]
		self.velocityInitPos   = param['arm_waypoints']['init_pos'  ][4]
		self.coordinaInitPos   = param['arm_waypoints']['init_pos'  ][5]
		self.waypointBackAvo   = param['arm_waypoints']['back_avo'  ][0:4]
		self.velocityBackAvo   = param['arm_waypoints']['back_avo'  ][4]
		self.coordinaBackAvo   = param['arm_waypoints']['back_avo'  ][5]
		self.waypointDaubStart = param['arm_waypoints']['daub_start'][0:4]
		self.velocityDaubStart = param['arm_waypoints']['daub_start'][4]
		self.coordinaDaubStart = param['arm_waypoints']['daub_start'][5]
		self.waypointDaubEnd   = param['arm_waypoints']['daub_end'  ][0:4]
		self.velocityDaubEnd   = param['arm_waypoints']['daub_end'  ][4]
		self.coordinaDaubEnd   = param['arm_waypoints']['daub_end'  ][5]
		self.waypointRight     = param['arm_waypoints']['right'     ][0:4]
		self.velocityRight     = param['arm_waypoints']['right'     ][4]
		self.coordinaRight     = param['arm_waypoints']['right'     ][5]
		self.waypointMiddle    = param['arm_waypoints']['middle'    ][0:4]
		self.velocityMiddle    = param['arm_waypoints']['middle'    ][4]
		self.coordinaMiddle    = param['arm_waypoints']['middle'    ][5]
		self.waypointLeft      = param['arm_waypoints']['left'      ][0:4]
		self.velocityLeft      = param['arm_waypoints']['left'      ][4]
		self.coordinaLeft      = param['arm_waypoints']['left'      ][5]
		self.waypointLeft2     = param['arm_waypoints']['left2'     ][0:4]
		self.velocityLeft2     = param['arm_waypoints']['left2'     ][4]
		self.coordinaLeft2     = param['arm_waypoints']['left2'     ][5]
		self.waypointDetect = self.waypointLeft
		self.velocityDetect = self.velocityLeft
		self.coordinaDetect = self.coordinaLeft

		self.velArmGlobalPercent  = param['speed']['arm_global_percent'  ] # 全局机械臂速度 [%]{0-100}
		self.velArmGlobalAcc      = param['speed']['arm_global_acc'      ] # 全局机械臂加速度 [%]{0-100}
		self.velMoverearToDaubEnd = param['speed']['moverear_to_daub_end'] # 到抹浆结束位速度
		self.velMoverearToBackAvo = param['speed']['moverear_to_back_avo'] # 到后避让点速度
		self.velMoverearToInitPos = param['speed']['moverear_to_init_pos'] # 到初始位速度
		self.velFetchAttach       = param['speed']['fetch_attach'        ] # 抓砖时触砖速度
		self.velLayAttach         = param['speed']['lay_attach'          ] # 铺砖时触di速度
		self.velFetchDown         = param['speed']['fetch_down'          ] # zhuazhuan下降速度
		self.velDescend           = param['speed']['descend'             ] # dingweiqian下降速度
		self.velStepback          = param['speed']['stepback'            ] # 后退速度
		self.velPaverDeliver      = param['speed']['paver_deliver'       ] # 搅笼转速
		self.velLocaAdjFetch      = param['speed']['loca_adjust_fetch'   ] # 抓砖对齐速度
		self.velLocaAdjLay        = param['speed']['loca_adjust_lay'     ] # 铺砖对齐速度

		self.fetchHeightSensorFixed   = param['fetch']['sensor_fixed_height']
		self.fetchHeightAlign         = param['fetch']['align_height'       ]
		self.fetchPressureGrasp       = param['fetch']['grasp_pressure'     ]
		self.fetchScannerToEdgeMargin = param['fetch']['scanner_edge_marin' ]

		self.stepbackMaxDistance    = param['stepback']['max_back_distance'  ]
		self.stepbackArmShiftDistY  = param['stepback']['arm_shift_left_dist']
		self.stepbackArmShiftDistX  = param['stepback']['arm_shift_front_dist']

		self.layPressureLoose = param['lay']['loose_pressure']
		self.layTimeVibrate   = param['lay']['vibrate_time'  ]

		self.locaRefSide    = param['localization']['refer_side'      ]
		self.locaVelScan    = param['localization']['scan_speed'      ]
		self.locaVelMove    = param['localization']['move_speed'      ]
		# self.locaLonStart   = param['localization']['start_point_lon' ]
		self.locaLonEnd     = param['localization']['end_point_lon'   ]
		self.locaLonDetect = param['localization']['detect_point_lon']
		# self.locaLatStart   = param['localization']['start_point_lat' ]
		self.locaLatEnd     = param['localization']['end_point_lat'   ]

		self.fetchLocaOffsetA  = param['fetch_loca_offset']['da']
		self.fetchLocaOffsetX  = param['fetch_loca_offset']['dx']
		self.fetchLocaOffsetY  = param['fetch_loca_offset']['dy']
		self.layLocaOffsetA  = param['lay_loca_offset']['da']
		self.layLocaOffsetX  = param['lay_loca_offset']['dx']
		self.layLocaOffsetY  = param['lay_loca_offset']['dy']

		self.layLocaOffsetALeft  = param['lay_loca_offset']['da_left']
		self.layLocaOffsetXLeft  = param['lay_loca_offset']['dx_left']
		self.layLocaOffsetYLeft  = param['lay_loca_offset']['dy_left']
		self.layLocaOffsetARight  = param['lay_loca_offset']['da_right']
		self.layLocaOffsetXRight  = param['lay_loca_offset']['dx_right']
		self.layLocaOffsetYRight  = param['lay_loca_offset']['dy_right']
		self.layLocaOffsetAMiddle  = param['lay_loca_offset']['da_middle']
		self.layLocaOffsetXMiddle  = param['lay_loca_offset']['dx_middle']
		self.layLocaOffsetYMiddle  = param['lay_loca_offset']['dy_middle']

		self.PrintParam()

	#--- print loaded values on screen
	def PrintParam(self):
		print(pt.white_bold+"--- Continuous motion control waypoints & velocities are as follows ---")
		print("   initPos: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaInitPos, self.waypointInitPos,  self.velocityInitPos ))
		print("   backAvo: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaBackAvo, self.waypointBackAvo,  self.velocityBackAvo ))
		print("daub start: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaDaubStart,self.waypointDaubStart, self.velocityDaubStart))
		print("daub   end: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaDaubEnd, 	self.waypointDaubEnd,   self.velocityDaubEnd  ))
		print("     right: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaRight,  	self.waypointRight,     self.velocityRight    ))
		print("    middle: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaMiddle, 	self.waypointMiddle,    self.velocityMiddle   ))
		print("      left: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaLeft, 	self.waypointLeft,      self.velocityLeft     ))
		print("     left2: coord [{}] waypoint {}  velocity [{}]".format(self.coordinaLeft2, 	self.waypointLeft2,     self.velocityLeft2    ))
		print("---")
		print(pt.default)
