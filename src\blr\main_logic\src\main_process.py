#! /usr/bin/env python3

"""
---------------------------------------------- INSTRUCTIONS -----------------------------------------------------
     AUTHOR: <PERSON>, All rights reserved by; and can only be applied for BLR projects by TOTEM company.
    VERSION: BLR#1
  CONSTRUCT: Created on 20211017, developed on 20220414-0507, debug on 202206-08
DESCRIPTION: In this program, a class definition and main function are included.
             The main functions are listed as following:
             1. ROS msg. and srv. mechanisms are realized;
             2. Global and inner varialbes are defined in the class;
             3. The workflow parameters are entirely copied into the class;
             4. The interface functions like control main arm and base are in the class;
             5. Step functions that are applied to achieve each step of workflow are in class;
             6. ROS action mechanism is realized to execute step functions accordingly.
    VERSION: BLR#3-1 (The 1st robot that works in JuRong railway station)
  CONSTRUCT: Created on 20220905, developed on 202209-, debug on 202209
DEVELOPMENT: Base on the above functions which are developed for BLR#1, optimizations are made as following:
             1. Organize the parameters in a further step;
             2. Split substep functions apart so that user software can operate tiling work in a more fine style;
             3. Enhance the Human Intervention function for actual use;
             4. Optimize the fetch step to successfully grasp the tiles even when they are overlaid too thick;
             5. Add the paverSlope parameter so as to realize non-horizontal scraping
             6. Delete IMU subscription contents, since workend leveling is performed by "CONTROL_BASE" node
             7. Improve the paver leveling and workend leveling control mode (leveling with a slope)
             8. Improve the joint control of main arm by adding an incremental joint control mode
             9. Add daubing height by adding "param" besides "step" and "substep"
            10. Add pitch bias parameter to adjust pitch angle while performing workendleveling in step DAUB and DESCEND
    VERSION: BLR#3-2 (the 2nd robot that is capable to work)
  CONSTRUCT: Created on 20230107, developed on 20230107, debug on 20230108
DEVELOPMENT: Base on the above functions which are developed for BLR#3, optimizations are made as following:
             1. Add GetLaySide() function to judge which side the main arm is on;
             2. Add AngleBiasBasedLinearMotionControl() to take oriental bias into consideration; [TODO]
             3. Increase wait time to ~4min after human intervention;
    VERSION: BLR#3-1/2
  CONSTRUCT: Optimized on 20230525
DEVELOPMENT: 1. Modify the big/small steps in working.
             2. Correct computation of biasA/X/Y after moving params. from "image_positioning" to this package [arm coord:X-front;Y-left;Z-?]     
             3. Modify the control of X/Y directions in human intervention
             4. Modify pressing down and checking pressure substep, just three steps further and wait longer time.
             5. [0624] Add the stepback distance compensation based on CV detection.
             6. [0630] Add parameters of pressing down distance & velocity while vibrating in STEP_lay
             7. [0630] Jump over the base leveling substep when being executed as a whole step.
             8. [0630] Try to turn on paver deliver & leveling in advance without checking resutls in STEP_moveRear, so as to save time.
    VERSION: BLR#2 (new and final)
  CONSTRUCT: Optimized on 20230809
DEVELOPMENT: 1. [0809] Add continuous motion control functions in "arm_drive.py", and test for robot arm;
             2. [0810] Repalce the Step 2 with continuous motion and auto. leveling execution;
             3. [0811] Add new line waypoints in yaml file, and remove all old joint waypoints.
    VERSION: BLR#2/3 (final version)
  CONSTRUCT: Optimized on 20231010
DEVELOPMENT: 1. [1010] Add three new parameters for L/M/R angular adjustment in anglue correction of CV;
             2. [1010] Add a new msg. subscription "hi_adjust" for HI adjustment before laying;
             3. [1010] Add callback function to adjust pose of workend;
             4. [1010] Remove all previously used waypoints for each substep;
             5. [1010] Remove "ExecMoveTo()", instead, use new "ad.ControlMainArm()" to control movement in substeps;
    VERSION: BLR#2/3 (final version)
  CONSTRUCT: Optimized on 20231106
DEVELOPMENT: 1. [1106] Use laserscan positioning instead of CV positioning;
             2. [1106] Update control_base's commands to in accordance with it;
             3. [1106] Adjust steps and substeps accordingly;
             4. [1109] Subscribe peripheral_sensor to get scanner heights, PSD and pressure.
    VERSION: BLR#4 (ultimate version)
  CONSTRUCT: Optimized on 20240308
DEVELOPMENT: 1. The code will be managed by GIT hereafter
------------------------------------------------------------------------------------------------------------------
"""
#朱总语录
#1.高度决定角度，角度决定态度
#2.问题没解决还想吃饭？
#3.加个班解决，今晚一定要处理好
#4.以后芬妮100万又有什么关系呢
#5.很简单
#6.给你这么好的平台，现在就要谈感恩
#7.舍得舍得，先舍后得
#8.你是要当管理的，带团队
#9.我们是全国第一
#10.实习期：答应你的可以给，我朱从兵说话算数。   转正后：不能给你转正，没达到我想要的效果，你不稳定，你听我说。  
#朱总擅长：隐藏情绪，利益捆绑，PUA，道德捆绑，钱给不到位。  不擅长：说话算数，   
#如果你是新来的实习生，咱们公司的现状是驱动器，电机，相机，控制器等等钱没有给供应商到位，对员工：工资随意克扣，强制加班，单休，约定薪资是狗屎，连博士硕士薪资都欠一大堆。
#如果你追求梦想选择了机器人，我相信其他地方会有更合适的舞台，如果你得待几个月，来这里希望你可以学到：1.享受冲突 2.人性-永远相信利益关系 3.我希望你保持善良。
#--- import libs
import time
import os
import sys
import math

import rospy
import actionlib                # this file includes 'SimpleActionServer'
from   main_logic.msg           import taskAction, taskGoal, taskResult, taskFeedback
from   std_msgs.msg             import Int32MultiArray, Float32MultiArray, String
from   control_base.msg         import sensor_data
# from   image_positioning.srv    import imageProcess  # first part means dir: image_positioning/srv

#--- the following two lines are applied to add env. path of 'lib' folder for 'import lib'
dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(dir)
from   lib                      import arm_drive  as ad          # customed lib file for controlling multi-DOF robot arm

#--- find the files in src/ directory running through any way
sys.path.append(os.path.dirname(os.path.realpath(__file__)))

from   blr_common               import STEP, TransEnumStep, MOD, RET, CMD,TransEnumCmd, REPLY,TransEnumReply, SWITCH, PARAM
from   print_type               import PRINT_TYPE as pt

'''
------------------------------  CLASS DEFINITION OVERVIEW ----------------------------------------
class TASK():

    global variables

    __init__(self, param:PARAM, srvNameCV, srvNameHI, msgPubBaseCmd, msgSubBaseStatus, msgSubImu, msgSubSensors)

    CallbackSensors(self, data:sensor_data)
    CallbackBaseReply(self, data:Int32MultiArray)
    CallbackXbox(self, data:Joy)
    RequestImageProcess(self, cameraIndex)
    RequestHumanIntervention(self)
    Callback_Action(self, cmd)

    CheckTimeoutAndPreempt(self, timeStart, timeThresh, description)
    ControlMainArmUntilFinishJoint(self, mod, posArray, vel)
    ControlMainArmUntilFinishLinear(self, mod, dx, dy, dz, da, vel)
    MainArmKinematics(self, dx, dy, da)
    GetLaySide(self)
    AngleBiasBasedLinearMotionControl(self, j1deg, j4deg, moveX, moveY)

x   AcquireBase(self, query, *args)
    ControlBase(self, cmd, switch, *args)   #(vel, pos) or (pos, tol)
    def ScanLocalization(self, cmd, tileLen, tileWidth, type, speedmove, speedscan, lonStart, lonEnd, latStart, latEnd)
    CheckBaseControlRec(self, cmd, timeThresh)
    CheckBaseControlResult(self, cmd, timeThresh)

    StepExecMoveFront(self, step, substep)                        # ONLY for single substeps execution
    StepExecMoveFront2(cmd.substep, cmd.layside, cmd.daubheight)  # ONLY for one step        execution
    StepExecMoveRear(self, substep)                               # ONLY for single substeps execution
    StepExecMoveRear2(self)                                       # ONLY for one step        execution

    StepExecStepback(self, substep, dist)
    StepExecFetch(self, substep)
    StepExecFetch2(self, substep)  [20240311]
    StepExecLay(self,substep)

x   StepExecLocaFine(self, substep, angleOrPos)
x   StepExecAdjHuman(self)
    StepExecLocaAdjust(self, substep)    [20231110]
    StepExecLocaAdjust2(self, substep)   [20240311]

x   StepExecOneLoop(self, laySide)
x   StepExecAll(self, height)

    AcqPosArm(self)
    TestMainArmLinearAbs(self, moveY)
    TestMainArmLinearInc(self)
    TestMyKineLinearCtrl(self)
    TestContinuousManip(self)

----------------------------------------------------------------------------------------------------
'''

#=======================================================================================================
#==============================  CLASS TASK DEFINITION HEREAFTER  ======================================
#=======================================================================================================

class TASK():

    #--- global varialbes inside this cless definition
    WORKEND_TRANS_INSTALLED = 1      # 1:use translational movement mechanism to adjust x/y bias before laying tiles
    RAD2DEG           = 57.29578     # angle with unit rad, multiply this value to get angle with deg
    
    #--- param: parameters of class PARAM() is transmitted inside
    # def __init__(self, param:PARAM, srvNameCV, srvNameHI, msgPubBaseCmd, msgSubBaseStatus, msgSubJoy):
    def __init__(self, param:PARAM, srvNameCV, srvNameHI, msgPubBaseCmd, msgPubBaseErr, msgSubBaseStatus, msgSubHi, msgSubSensors):

        #--- action server creation
        #--- await task request and execute tiling task when received it
        #--- put service function here which includes all tiling work execution
        rospy.loginfo(pt.green_bold+"[main] create task action server, and await task request as always..."+pt.default)
        self.actionServer = actionlib.SimpleActionServer('task', taskAction, self.Callback_Action, False)
        self.actionServer.start()

        # #--- service client of image processing (CV: Computer Vision)
        # rospy.loginfo(pt.green_bold+"[main] load in service of Computer Vision [%s]..." % srvNameCV +pt.default)
        # #rospy.wait_for_service(srvNameCV)  # 'image_process'
        # try:
        #     self.srvClientCV = rospy.ServiceProxy(srvNameCV, imageProcess) # 'image_process'
        # except rospy.ServiceException as e:
        #     print(pt.red_bold+"[main] service call failed in [%s]: %s" % (srvNameCV, e) +pt.default)

        # #--- service client of human intervention (HI)
        # rospy.loginfo(pt.green_bold+"[main] load in service of Human Intervention [%s]..." % srvNameHI +pt.default)
        # #rospy.wait_for_service(srvNameHI)
        # try:
        #     self.srvClientHI = rospy.ServiceProxy(srvNameHI, imageProcess)
        # except rospy.ServiceException as e:
        #     print(pt.red_bold+"[main] service call failed in [%s]: %s" % (srvNameHI, e) +pt.default)

        #--- messages subscription and publication
        # rospy.Subscriber(msgSubJoy, Joy, self.CallbackXbox, queue_size=1) # '/joy'
        rospy.Subscriber(msgSubSensors, sensor_data, self.CallbackSensors) # 'peripheral_sensor'
        rospy.Subscriber(msgSubBaseStatus, Int32MultiArray, self.CallbackBaseReply, queue_size=4) # 'base_status'
        rospy.Subscriber(msgSubHi, Float32MultiArray, self.CallbackHi, queue_size=1) # 'hi_adjst'
        self.pubBaseCtrl  = rospy.Publisher(msgPubBaseCmd, Int32MultiArray, queue_size=5) # 'base_cmd'
        self.pubBaseError = rospy.Publisher(msgPubBaseErr, String, queue_size=2) # 'base_error'

        #--- global variables init.
        rospy.loginfo(pt.cyan_bold+"[main] initialize global parameters and load in workflow parameters..."+pt.default)
        self.result   = taskResult()
        self.feedback = taskFeedback()

        self._baseStatus = [0 for i in range(CMD.end.value)]

        self.param = param                      # class PARAM is loaded in

        #--- Human Intervention
        self._confirm = False
        self._lockHi  = False                   #kun_test          
        self._flagManip = False
        
        #--- localization
        self._result = 'null'                   # status of image positioning
        self._biasX = 0                         # bias computed by image positioning
        self._biasY = 0
        self._biasA = 0

        #--- continuous motion control using zMotion
        self._nodeRest = 0
        self._nodesAll = 0

        self._tileLenLat = 0       # obtained from fetch localization
        self._tileLenLon  = 0
        self._toEdgeLon  = 0
        self._toEdgeLat  = 0
        self._scanStartPosLon = 0
        self._scanStartPosLat = 0
        self._pitch = 0
        self._roll  = 0
        self._biasZ = 0

        #--- sensor data
        # self._distLaser = [0 for i in range(6)] # front right rear left another in sequence
        self._scannerHeightLeft   = 0
        self._scannerHeightRear   = 0
        self._scannerHeightRight  = 0
        self._scannerHeightFLeft  = 0
        self._scannerHeightFRight = 0
        self._scannerHeightHead   = 0
        self._psdLeft  = 0
        self._psdRight = 0
        self._psdFront = 0
        self._psdRear  = 0
        self._pressure = 0

        #--- update coordinate pose in global list
        poseCoord = ad.GetCurrentPosition2(1)
        rospy.loginfo("Init current global coordinate pose as [X:%.2f, Y:%.2f, Z:%.2f, U:%.2f]" % \
            (poseCoord[0], poseCoord[1], poseCoord[2], poseCoord[3]))

        rospy.loginfo(pt.bold+"[main] main node initialization is completed and is running..."+pt.default)
        rospy.loginfo(pt.default)


    #==============================  CALLBACK FUNCTIONS HEREAFTER  =========================================

    def CallbackSensors(self, data:sensor_data):
        # self._scannerHeightLeft   = data.laser_height[0]
        # self._scannerHeightRight  = data.laser_height[1]
        # self._scannerHeightRear   = data.laser_height[2]
        # self._scannerHeightFLeft  = data.laser_height[3]
        # self._scannerHeightFRight = data.laser_height[4]
        # self._scannerHeightHead   = data.laser_height[5]
        self._psdLeft  = data.psd_four[0]
        self._psdRight = data.psd_four[1]
        self._psdFront = data.psd_four[2]
        self._psdRear  = data.psd_four[3]
        self._pressure = data.pressure

    def CallbackBaseReply(self, data:Int32MultiArray):
        index = data.data[0]
        value = data.data[1]
        self._baseStatus[index] = value
        if(value == REPLY.running.value):
            self._nodeRest = data.data[2]
            self._nodesAll = data.data[3]
        elif(value != REPLY.succeeded.value):
            return
        if(index == CMD.scanLoca.value):
            self._biasA = data.data[2] / 100.0
            self._biasX = data.data[3] / 100.0
            self._biasY = data.data[4] / 100.0
            self._tileLenLat = data.data[5] / 100.0 if(data.data[5] > 0.0) else self._tileLenLat    # lat dir.
            self._tileLenLon = data.data[6] / 100.0 if(data.data[6] > 0.0) else self._tileLenLon    # lon dir.
            self._toEdgeLon  = data.data[7] / 100.0 if(data.data[7] > 0.0) else self._toEdgeLon
            self._toEdgeLat  = data.data[8] / 100.0 if(data.data[8] > 0.0) else self._toEdgeLat
            rospy.loginfo(pt.white_bold + "--- Receive scanning bias A[%.2f]deg X[%.2f]mm Y[%.2f]mm | tile len lat/lon[%.1f x %.1f] | toEdgeLon/Lat[%.1f, %.1f]---"
                           % (self._biasA, self._biasX, self._biasY, self._tileLenLat, self._tileLenLon, self._toEdgeLon, self._toEdgeLat))
        elif(index == CMD.scanLoca2.value):
            self._biasA = data.data[2] / 100.0
            self._biasX = data.data[3] / 100.0
            self._biasY = data.data[4] / 100.0
            self._tileLenLat = data.data[5] / 100.0 if(data.data[5] > 0.0) else self._tileLenLat    # lat dir.
            self._tileLenLon = data.data[6] / 100.0 if(data.data[6] > 0.0) else self._tileLenLon    # lon dir.
            self._pitch = data.data[7] / 100.0
            self._roll  = data.data[8] / 100.0
            self._biasZ = data.data[9] / 100.0
            self._scannerHeightFLeft  = data.data[10] / 100.0
            self._scannerHeightFRight = data.data[11] / 100.0
            self._scannerHeightRight  = data.data[12] / 100.0
            self._scannerHeightLeft   = data.data[13] / 100.0
            self._scannerHeightRear   = data.data[14] / 100.0
            # rospy.loginfo(pt.white_bold + "--- Receive scanning bias A[%.2f]deg X[%.2f]mm Y[%.2f]mm Z[%.2f]mm ---" % (self._biasA, self._biasX, self._biasY, self._biasZ))
            # rospy.loginfo(pt.white_bold + "--- Receive scanning pitch/roll [%.1f / %.1f]mdeg ---" % (self._pitch, self._roll))
            # rospy.loginfo(pt.white_bold + "--- Receive scanning tile len lat/lon [%.1f x %.1f] ---" % (self._tileLenLat, self._tileLenLon))
            # rospy.loginfo(pt.white_bold + "--- Receive scanning heights: FL[%.1f]/FR[%.1f]/R[%.1f]/L[%.1f]/RC[%.1f]mm ---"
            #     % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight, self._scannerHeightLeft, self._scannerHeightRear))
        return

    def CallbackHi(self, data:Float32MultiArray):
        if(self._lockHi):
            print(pt.yellow + "Ignore Hi adjustment, due to lock status." + pt.default)
            return
        adjX = data.data[0]
        adjY = data.data[1]
        adjA = data.data[2]
        flag = data.data[3]
        print(pt.default + "adjX: %.1f adjY: %.1f adjA: %.1f confirm: %.0f" % (adjX, adjY, adjA, flag))
        if(flag):
            print("Confirmation from Hi reveived.")
            self._confirm = True
            self._lockHi  = True
            return
        if((adjX == 0.0) and (adjY == 0.0) and (adjA == 0.0)):
            print("Stop and cancel currently-running continuous motion.")
            ad.ContinuousMotionStop()
        else:
            self._flagManip = True
            if(adjA != 0.0):
                goalA = -45 if(adjA>0) else 45
                self.ControlMainArmLinear(MOD.inc.value, 0, 0, 0, goalA, abs(adjA))
            else:
                goalX = -20 if(adjX>0) else 20
                goalY = -20 if(adjY<0) else 20
                vel = max(abs(adjX), abs(adjY))
                self.ControlMainArmLinear(MOD.inc.value, goalX, goalY, 0, 0, vel)


    #----------------------------------------------------------------------------------
    # I - command type:
    #     sensor msgs from sensoring nodes: psd, imu, and manipulator pose measure data
    # O - motion control values & status info. of working process
    # R - null
    # i -
    #----------------------------------------------------------------------------------
    def Callback_Action(self, cmd):
        # rospy.loginfo(pt.white_bold+"[main] task request [%d][%s] received, start to work..." % (cmd.step, TransEnumStep(cmd.step)))
        print(pt.default + " ")

        if(cmd.step == STEP.null.value):
            ret = True
        elif(cmd.step == STEP.stop.value):
            ad.ServoPowerOff()
            self.ControlBase(CMD.allStop.value, SWITCH.on.value)
            ret = True
        elif(cmd.step == STEP.pause.value):
            # this function is realized by action: cancel()
            ret = True
        elif(cmd.step == STEP.unpause.value):
            # this function is realized by action: sendgoal()
            ret = True
        elif(cmd.step == STEP.ready.value):
            ad.ServoPowerOn()
            ret = True
        elif(cmd.step == STEP.loadParam.value):
            self.param.LoadParamsFromYaml(dir + "/cfg/tiling.yaml")
            ret = True

        #--- macro steps with a serial of steps
        elif(cmd.step == STEP.macroAll.value):
            ret = self.StepExecAll(cmd.daubheight)
        elif(cmd.step == STEP.macroMiddle.value):
            ret = self.StepExecOneLoop('middle')
        elif(cmd.step == STEP.macroRight.value):
            ret = self.StepExecOneLoop('right')
        elif(cmd.step == STEP.macroLeft.value):
            ret = self.StepExecOneLoop('left')

        #--- normal steps which needs main arm's movements
        elif(cmd.step == STEP.moveRear.value):
            ret = self.StepExecMoveRear(cmd.substep)
        elif(cmd.step == STEP.moveRear2.value):
            ret = self.StepExecMoveRear2()
        elif(cmd.step == STEP.moveFront.value): # [20230810]
            ret = self.StepExecMoveFront2(cmd.layside, cmd.daubheight)
        elif(cmd.step == STEP.descend.value):   # [20230810]
            # ret = self.StepExecDescend(cmd.substep)
            ret = self.StepExecDescend2(cmd.substep)
        elif(cmd.step == STEP.moveMiddle.value):
            ret = self.StepExecMoveFront(cmd.step, cmd.substep, cmd.daubheight)
        elif(cmd.step == STEP.moveRight.value):
            ret = self.StepExecMoveFront(cmd.step, cmd.substep, cmd.daubheight)
        elif(cmd.step == STEP.moveLeft.value):
            ret = self.StepExecMoveFront(cmd.step, cmd.substep, cmd.daubheight)
        elif(cmd.step == STEP.moveLeft2.value):
            ret = self.StepExecMoveFront(cmd.step, cmd.substep, cmd.daubheight)
        
        #--- adjustment of pose of workend according to CV and HI
        elif(cmd.step == STEP.moveAdjFineAngle.value):
            ret = self.StepExecLocaFine(cmd.substep, 'angle')
        elif(cmd.step == STEP.moveAdjFinePos.value):
            ret = self.StepExecLocaFine(cmd.substep, 'pos')
        elif(cmd.step == STEP.moveAdjHuman.value):
            ret = self.StepExecAdjHuman()
        elif(cmd.step == STEP.locaAdjust.value):
            # ret = self.StepExecLocaAdjust(cmd.substep)
            ret = self.StepExecLocaAdjust2(cmd.substep)

        #--- normal steps of others
        elif(cmd.step == STEP.stepback.value):
            ret = self.StepExecStepback(cmd.substep)
        elif(cmd.step == STEP.fetch.value):
            # ret = self.StepExecFetch(cmd.substep)
            ret = self.StepExecFetch2(cmd.substep)
        elif(cmd.step == STEP.lay.value):
            ret = self.StepExecLay(cmd.substep)
        elif(cmd.step == STEP.flatten.value):
            ret = False

        #--- acquisition and test tasks
        elif(cmd.step == STEP.acqPosArm.value):
            ret = self.AcqPosArm()
        elif(cmd.step == STEP.testLinearAbsCtl.value):
            ret = self.TestMainArmLinearAbs(cmd.param)
        elif(cmd.step == STEP.testLinearIncCtl.value):
            ret = self.TestMainArmLinearInc()
        elif(cmd.step == STEP.testMyKineCtrl.value):
            ret = self.TestMyKineLinearCtrl()
        elif(cmd.step == STEP.testContinuousManip.value):
            ret = self.TestContinuousManip(0)
        elif(cmd.step == STEP.testContinuousManipBack.value):
            ret = self.TestContinuousManip(10)
        elif(cmd.step == STEP.testContiManipStop.value):
            ret = self.TestContinuousManip(1)
        elif(cmd.step == STEP.testContiManipSuspend.value):
            ret = self.TestContinuousManip(2)
        elif(cmd.step == STEP.testContiManipStart.value):
            ret = self.TestContinuousManip(3)
        else:
            ret = False
            rospy.logerr("no such step [%d][%s] with substep[%d]" % (cmd.step, TransEnumStep(cmd.step), cmd.substep))

        if(ret is True):
            self.result.state = "succeeded"
            self.actionServer.set_succeeded(self.result, "Task [%s][%d] completed successfully." % (TransEnumStep(cmd.step), cmd.substep))
            # rospy.loginfo(pt.green+"[TASK] Succeed in task step [%s] substep[%d]" % (TransEnumStep(cmd.step), cmd.substep))
        else:
            self.result.state = "failed"
            self.actionServer.set_aborted(self.result, "Task [%s][%d] failed." % (TransEnumStep(cmd.step), cmd.substep))
            rospy.logerr("[TASK] Failed in task step [%s] with substep[%d]" % (TransEnumStep(cmd.step), cmd.substep))


    def CheckTimeoutAndPreempt(self, timeStart, timeThresh, description):
        '''
        timeout quit and goal cancel mechanism
        '''
        timeNow = time.time()
        if(timeNow - timeStart > timeThresh):
            rospy.logwarn("Timeout [%d]s during %s!" % (timeThresh, description))
            return RET.timeout.value
        elif(self.actionServer.is_preempt_requested()):
            self.result.state = 'preempted'
            self.actionServer.set_preempted(self.result, "Cancel command is received and hence the task is terminated.")
            rospy.logwarn("Goal canceled during %s." % description)
            return RET.cancel.value
        else:
            return RET.normal.value

    def ControlMainArm(self, coord, mode, poseGoal, vel):
        # 改为通过control_base控制机械臂
        if coord == 0:  # 关节坐标模式
            self.ControlBase(CMD.armJoint.value, SWITCH.on.value, mode, vel, poseGoal[0], poseGoal[1], poseGoal[2], poseGoal[3])
        else:  # 直线坐标模式
            self.ControlBase(CMD.armLinear.value, SWITCH.on.value, mode, vel, poseGoal[0], poseGoal[1], poseGoal[2], poseGoal[3])

    def ControlMainArmJoint(self, mode, poseGoal, vel):
        ad.ControlMainArmJoint(mode, poseGoal, vel, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)

    def ControlMainArmLinear(self, mode, dx, dy, dz, da, vel):
        return ad.ControlMainArmLinear(mode, dx, dy, dz, da, vel, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)

    def ControlMainArmUntilFinishJoint(self, mod, posArray, vel):
        '''
        Main arm control, in 'joint' mode specifically
        posArray = [j1,j2,j3,j4], which are four angluar positions, all with unit: deg; tol(erance) unit: deg; timeout unit: s
        i.e. j1: base rotary joint, j2:vertical translational joint, j3: horizontal translational joint, and j4: workend rotary joint
        Positive direction definitions (birdview): j1: CCW; j2: descend; j3: reach out; j4: CCW
        '''
        posGoal = self.ControlMainArmJoint(mod, posArray, vel)
        timeStart = time.time()
        print(pt.default + "Rest nodes", end=': ', flush=True)
        while True:
            time.sleep(1)
            ret = self.CheckTimeoutAndPreempt(timeStart, 60, "joint control")
            if(ret == RET.timeout.value):
                return False
            elif(ret == RET.cancel.value):
                ad.ContinuousMotionStop()
                ad.ServoPowerOff()
                return False

            # ret1 = ad.CheckArmArrival(posGoal, 0, self.param.toleranceArmMoveDist, self.param.allTolAngleMainArm)
            # if(ret1):
            #     return True
            restNodes = ad.GetRestContinuousNodes()
            print(restNodes, end=',', flush=True)
            if(restNodes == 0):
                print("---|")
                return True

    def ControlMainArmUntilFinishLinear(self, mod, dx, dy, dz, da, vel):
        '''
        Main arm control, in 'linear' incremental(0) or absolute(1) position mode specifically
        dx, dy, dz are 3 axis incremental positions with unit mm, and 'da' is incremental Rz angle with unit: deg; tol(erance) unit: deg; timeout unit: s
        Positive direction definitions (birdview): X: orientation of robot head; Y: left side; Z: up; U: CW
        '''
        posGoal = self.ControlMainArmLinear(mod, dx, dy, dz, da, vel)
        timeStart = time.time()
        print(pt.default + "Rest nodes", end=': ', flush=True)
        while True:
            time.sleep(0.5)
            ret = self.CheckTimeoutAndPreempt(timeStart, 120, "main arm linear control")
            if(ret == RET.timeout.value):
                return False
            elif(ret == RET.cancel.value):
                # 机械臂停止命令已迁移到control_base
                self.ControlBase(CMD.armStop.value, SWITCH.on.value)
                return False
            
            # ret1 = ad.CheckArmArrival(posGoal, 1, self.param.toleranceArmMoveDist, self.param.allTolAngleMainArm)
            # if(ret1):
            #     return True
            restNodes = ad.GetRestContinuousNodes()
            print(restNodes, end=',', flush=True)
            if(restNodes == 0):
                print("---|")
                return True

    def WaitContinuousMotionFinish(self, timeout):
        timeStart = time.time()
        print(pt.default + "Rest nodes", end=': ', flush=True)
        while True:
            time.sleep(0.5)
            ret = self.CheckTimeoutAndPreempt(timeStart, timeout, "main arm continuous control (single goal)")
            if(ret == RET.timeout.value):
                return False
            elif(ret == RET.cancel.value):
                ad.ContinuousMotionStop()
                ad.ServoPowerOff()
                return False

            restNodes = ad.GetRestContinuousNodes()
            print(restNodes, end=',', flush=True)
            if(restNodes == 0):
                print("---|")
                rospy.loginfo(pt.green + "Continuous goal arrival!" + pt.default)
                return True


    def MainArmKinematics(self, dx, dy, da):
        '''
        This function creates the Kinematics mode of main arm, and calculates each joint's absolute goal based on the model.
        DX/DY/DA are incremental movements that need to be achieved, with units: mm (DX/DY) and deg(DA)
        j1 and j4 are rotatory joints on the robot base and workend respectively. j3 is translational joint.
        [j1, j2(original value), j3, j4] will be returned back in sequence, all with unit deg and mm
        '''
        L0 = 1209 #[mm] arm length from j1 to j4 when acquired length is 0 from iNexBot controller
        posJoints = ad.GetCurrentPosition(0) # unit: deg(j1,j4) and mm(j2,j3)
        j1AngNow = posJoints[0] / TASK.RAD2DEG # unit: rad
        j2LenNow = posJoints[1] # unit: mm
        j3LenNow = posJoints[2] # unit: mm
        j4AngNow = posJoints[3] # unit: deg
        rospy.logwarn("now pose: [j1:%.2fdeg, j2:%.2fmm, j3:%.2fmm, j4:%.2fdeg]" % (posJoints[0], posJoints[1], posJoints[2], posJoints[3]))
        coordX = (L0 + j3LenNow) * math.cos(j1AngNow) + dx
        coordY = (L0 + j3LenNow) * math.sin(j1AngNow) + dy
        j3LenGoal = math.sqrt(coordX**2 + coordY**2) - L0       # unit: mm
        j1AngGoal = math.atan2(coordY, coordX) * TASK.RAD2DEG   # unit: deg
        j4AngGoal = -(j1AngGoal - j1AngNow*TASK.RAD2DEG) + j4AngNow + da                  # unit: deg
        rospy.logwarn("goal pose: [j1:%.2fdeg, j2:%.2fmm, j3:%.2fmm, j4:%.2fdeg] with inc. xya [%.3fmm, %.3fmm, %.3fdeg]" \
            % (j1AngGoal, j2LenNow, j3LenGoal, j4AngGoal, dx, dy, da))
        return [j1AngGoal, j2LenNow, j3LenGoal, j4AngGoal]

    def GetLaySide(self):
        poseCoord = ad.GetCurrentPosition(1)
        valY = poseCoord[1]
        if(valY > 200):
            print(pt.default + "--- GetLaySide(): LEFT side with valY: %d." % valY)
            return "left"
        elif(valY < -200):
            print(pt.default + "--- GetLaySide(): RIGHT side with valY: %d." % valY)
            return "right"
        else:
            print(pt.default + "--- GetLaySide(): MIDDLE side with valY: %d." % valY)
            return "middle"

    def AngleBiasBasedLinearMotionControl(self, j1deg, j4deg, moveX, moveY):
        '''
        This function corrects control values in x- and y-direction, according to two joint angle j1 and j4.
        J1dEG, J4DEG: 1st and 4th angular joint's angle (unit:deg).
        MOVEX, MOVEY: distance correction according to CV positioning results (unit:mm).
        return MOVEX2, MOVEY2: corrected distance for motion.
        '''
        len = math.sqrt(moveX**2 + moveY**2)
        beta = (j1deg + j4deg)/57.29578 - math.atan2(moveX, moveY)
        sign = 1 if(moveY > 0) else -1
        moveX2 = -sign * math.sin(beta) * len
        moveY2 = -sign * math.cos(beta) * len
        print("---j1/j2:[%.3f,%.3f]deg; original xy:[%.1f,%.1f]mm; correct xy:[%.1f,%.1f]mm" % (j1deg,j4deg,moveX,moveY,moveX2,moveY2))
        return moveX2, moveY2

    #================================  CONTROL_BASE FUNCTIONS HEREAFTER  ==========================================

    def ScanLocalization(self, cmd, tileLen, tileWidth, type, speedmove, speedscan, lonStart, lonEnd, latStart, latEnd):
        '''
        This function sends commands via ROS topic '/base_cmd', to trigger execution of scan localization in control_base.
        '''
        self._baseStatus[cmd] = REPLY.null.value
        flagSwtichOn = True
        self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, flagSwtichOn, 
                                                        int(tileLen), 
                                                        int(tileWidth), 
                                                        int(type),
                                                        int(speedmove), int(speedscan),
                                                        int(lonStart), int(lonEnd), 
                                                        int(latStart), int(latEnd)]))

    def ControlBase(self, cmd, switch, *args):
        '''
        This function sends commands via ROS topic '/base_cmd', in order to control base modules.
        The last input parameter 'args' includes three factors: vel(mm/s), pos(mm), tol.
        These three factors is not always in a constant sequence.
        For example, in cmd:paverLeveling, it is organized as: pos, tol.
        However, in all other cmds that need 'args', they are organized as: vel, pos
        For workend leveling control, if no input arg of 'goalAngle', it will take 0 as default.
        '''
        self._baseStatus[cmd] = REPLY.null.value
        #--- control the modules that only need to turn on/off
        if( (cmd == CMD.allStop.value)          \
        or  (cmd == CMD.suck.value)             \
        or  (cmd == CMD.vibrate.value)          \
        or  (cmd == CMD.lamp.value)             ):
            self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        #--- localization method 4 (1-CV_3cams 2-CV_2cams 3-scan module 4-scanner product)
        elif(cmd == CMD.scanLoca2.value):
            if(switch == SWITCH.on.value):
                typeFetchOrLay = int(args[0]) # 1/11-fetch; 2-lay; 3-get out z heights; 4-get middle z heights; 5-get in z heights
                detectPoint = int(args[1]) if(typeFetchOrLay == 4) else 0
                if(typeFetchOrLay == 1):
                    tileLon = int(args[2])
                    tileLat = int(args[3])
                    self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, typeFetchOrLay, detectPoint, tileLon, tileLat]))
                else:
                    self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, typeFetchOrLay, detectPoint]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        #--- control the modules that need turn on with velocity/omega and position/angle
        elif(cmd == CMD.paverDeliver.value):
            if(switch == SWITCH.on.value):
                vel = int(args[0])
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, vel]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        elif((cmd == CMD.spin.value)      \
        or   (cmd == CMD.wheelRoll.value) ):
            vel = int(args[0])
            pos = int(args[1])
            self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, vel, pos]))
        elif(cmd == CMD.trackLine.value):
            if(switch == SWITCH.on.value):
                vel = int(args[0])
                pos = int(args[1])
                mod = 1 # use measured laser distance to get backstep distance
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, mod, vel, pos]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))

        #--- control the modules that need turn on with velocity and position/angle for EACH single component
        elif((cmd == CMD.supportLift.value) \
        or   (cmd == CMD.steerTrans.value)  \
        or   (cmd == CMD.steerTurn.value) ):
            vel = int(args[0])
            pos = int(args[1])
            self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, vel, vel, vel, vel, pos, pos, pos, pos]))
        #--- contorl the modules that need turn on with MODE, velocity and position/angle
        elif((cmd == CMD.paverLift.value)
        or   (cmd == CMD.paverRoll.value)):
            vel = int(args[0])
            pos = int(args[1])
            self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, 1, vel, pos]))    # 1: abspos mode
        #---control the modules that need turn on with tolerance
        elif(cmd == CMD.paverLeveling.value):
            if(switch == SWITCH.on.value):
                posLeft  = int(args[0])
                posRight = int(args[1])
                tol      = int(args[2])
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, posLeft, posRight, tol]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        elif(cmd == CMD.workendLeveling.value):
            if(switch == SWITCH.on.value):
                onceOrLoop = int(args[0])
                tol        = int(args[1])
                goalRoll   = 0 if(len(args) < 3) else args[2]
                goalPitch  = 0 if(len(args) < 4) else args[3]
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, onceOrLoop, tol, goalRoll, goalPitch]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        elif(cmd == CMD.supportLeveling.value):
            if(switch == SWITCH.on.value):
                arrayGoal      = int(args[0])
                threshTorque   = int(args[1])
                toleranceAngle = int(args[2])
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, arrayGoal, threshTorque, toleranceAngle]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        elif(cmd == CMD.workendAlignTilt.value):
            if(switch == SWITCH.on.value):
                tolAngle = int(args[0])
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, tolAngle]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        elif((cmd == CMD.workendTrans.value)
        or   (cmd == CMD.moveScanner.value )):
            if(switch == SWITCH.on.value):
                mode   = args[0]
                velLon = int(args[1])
                velLat = int(args[2])
                posLon = int(args[3])
                posLat = int(args[4])
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, mode, velLon, velLat, posLon, posLat]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        elif(cmd == CMD.workendJoints.value):
            if(switch == SWITCH.on.value):
                mode     = int(args[0])
                velPitch = int(args[1])
                velRoll  = int(args[2])
                posPitch = int(args[3])
                posRoll  = int(args[4])
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch, mode, velPitch, velRoll, posPitch, posRoll]))
            else:
                self.pubBaseCtrl.publish(Int32MultiArray(data = [cmd, switch]))
        else:
            rospy.logwarn("mismatch cmd [%d]" % cmd)


    def CheckBaseControlRec(self, cmd, timeThresh):
        timeStart = time.time()
        while True:
            ret = self.CheckTimeoutAndPreempt(timeStart, timeThresh, TransEnumCmd(cmd))
            if(ret == RET.timeout.value):
                return False
            elif(ret == RET.cancel.value):
                self.ControlBase(cmd, SWITCH.off.value)
                return False

            if((self._baseStatus[cmd] == REPLY.received.value)   \
            or (self._baseStatus[cmd] == REPLY.executed.value)   \
            or (self._baseStatus[cmd] == REPLY.preempted.value)  \
            or (self._baseStatus[cmd] == REPLY.terminated.value) ):
                rospy.loginfo(pt.default+"[%s] got the response of base control." % TransEnumCmd(cmd))
                return True

    def CheckBaseControlResult(self, cmd, timeThresh):
        '''
        This function checks the replied msg, and find the target status(string).
        The new upcoming status will replace the old one.
        '''
        # self._baseStatus[cmd] = REPLY.null.value
        timeStart = time.time()
        while(True):
            ret = self.CheckTimeoutAndPreempt(timeStart, timeThresh, TransEnumCmd(cmd))
            if(ret == RET.timeout.value):
                return False
            elif(ret == RET.cancel.value):
                if(cmd < CMD.axisPosition.value):
                    self.ControlBase(cmd, SWITCH.off.value)
                return False

            if((self._baseStatus[cmd] == REPLY.failed.value)     \
            or (self._baseStatus[cmd] == REPLY.rejected.value)   \
            or (self._baseStatus[cmd] == REPLY.timeout.value)    \
            or (self._baseStatus[cmd] == REPLY.busy.value)       ):
                rospy.loginfo(pt.default+"[%s] %s." % (TransEnumCmd(cmd), TransEnumReply(self._baseStatus[cmd])))
                return False
            if( (self._baseStatus[cmd] == REPLY.succeeded.value)  \
             or (self._baseStatus[cmd] == REPLY.finished.value)   \
             or (self._baseStatus[cmd] == REPLY.terminated.value) \
             or (self._baseStatus[cmd] == REPLY.timedue.value)    ):
                # rospy.loginfo(pt.green_bold+"[%s] succeed in base control." % TransEnumCmd(cmd))
                return True


    #================================  TASK FUNCTIONS HEREAFTER  ===========================================


    def StepExecMoveRear(self, substep):
        '''
        This function controls robot arm to rear position from different three original places.
        All movements from front(left/middle/right) shares the same two transient points.
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 3
        self.feedback.step = STEP.moveRear.value

        #--- move to 1st transient point firstly
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Joint move to front avo point (daub end)." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # ret = self.ExecMoveTo(STEP.moveRear.value, 'transient1')
            self.ControlMainArm(self.param.coordinaDaubEnd, 1, self.param.waypointDaubEnd, self.param.velMoverearToDaubEnd)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True

        #--- move to 2nd transient point secondly
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Linear move to back avo point." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # ret = self.ExecMoveTo(STEP.moveRear.value, 'transient2')
            self.ControlMainArm(self.param.coordinaBackAvo, 1, self.param.waypointBackAvo, self.param.velMoverearToBackAvo)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True

        #--- move to the goal point finally
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Linear move to init pos point." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # ret = self.ExecMoveTo(STEP.moveRear.value, 'goal')
            self.ControlMainArm(self.param.coordinaInitPos, 1, self.param.waypointInitPos, self.param.velMoverearToInitPos)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            else:
                return True


    def StepExecMoveFront(self, step, substep, daubheight):
        '''
        This function controls the main arm to the transient point, and the goal point (left, right or middle);
        And after being at the goal point, adjust the pose of the workend according to the pose bias of the base.
        Finally, perform the workend leveling.
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 7
        self.feedback.step = step

        #--- move to the 1st transient point
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Move to init avo & transient point." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # ret = self.ExecMoveTo(STEP.daub.value, 'transient1')
            self.ControlMainArm(self.param.coordinaInitPos, 1, self.param.waypointInitPos, self.param.velocityInitPos)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            self.ControlMainArm(self.param.coordinaBackAvo, 1, self.param.waypointBackAvo, self.param.velocityBackAvo)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True

        #--- move to the daub above with x bias
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Move to daub above point with x bias." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            waypoint = self.param.waypointDaubStart.copy()
            marginLat = self.param.globalTileLat/2 if(self.param.globalFetchMarginLat == 0) else self.param.globalFetchMarginLat
            biasX = -(marginLat - self.param.globalSampleTileLat / 2.0)
            waypoint[0] += biasX
            self.ControlMainArm(self.param.coordinaDaubStart, 1, waypoint, self.param.velocityDaubStart)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            print(pt.yellow + "[Clean-Daub] bias [%.1f]mm in X dir. with sample tile lat [%.0f]mm and fetch margin lat[%.0f]mm"
                % (biasX, self.param.globalSampleTileLat, marginLat))

            if(substep == self.feedback.substep):
                return True
            
        #--- move to a start point
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Joint move to start point." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # ret = self.ExecMoveTo(STEP.daub.value, 'start')
            waypoint = self.param.waypointDaubStart.copy()
            marginLat = self.param.globalTileLat/2 if(self.param.globalFetchMarginLat == 0) else self.param.globalFetchMarginLat
            biasX = -(marginLat - self.param.globalSampleTileLat / 2.0)
            waypoint[0] += biasX
            waypoint[2] += daubheight
            self.ControlMainArm(self.param.coordinaDaubStart, 1, waypoint, self.param.velocityDaubStart)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            print(pt.yellow + "[Clean-Daub] Descend another [%.0f]mm to attach the daub roller" % (daubheight))
            if(substep == self.feedback.substep):
                return True

        #--- workend leveling continuously
        self.feedback.substep = 4
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Perform workend leveling." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            quitLevelingAfterAchieve = False
            tolAngle  = int(self.param.tolAngleWorkendLeveling)
            goalRoll  = 0
            goalPitch = 0
            self.ControlBase(CMD.workendLeveling.value, SWITCH.on.value, quitLevelingAfterAchieve, tolAngle, goalRoll, goalPitch)
            ret = self.CheckBaseControlResult(CMD.workendLeveling.value, self.param.timeoutWorkendLeveling)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True
            
        #--- move to the end point
        self.feedback.substep = 5
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Linear move to end point." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # ret = self.ExecMoveTo(STEP.daub.value, 'end')
            self.ControlMainArm(self.param.coordinaDaubEnd, 1, self.param.waypointDaubEnd, self.param.velocityDaubEnd)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True
            
        #--- move to a transient point
        self.feedback.substep = 6
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Joint move to transient point." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # ret = self.ExecMoveTo(step, 'transient')
            if(step == STEP.moveRight.value):
                self.ControlMainArm(self.param.coordinaRight, 1, self.param.waypointRight, self.param.velocityRight)
            elif(step == STEP.moveMiddle.value):
                self.ControlMainArm(self.param.coordinaMiddle, 1, self.param.waypointMiddle, self.param.velocityMiddle)
            elif(step == STEP.moveLeft.value):
                self.ControlMainArm(self.param.coordinaLeft, 1, self.param.waypointLeft, self.param.velocityLeft)
            elif(step == STEP.moveLeft2.value):
                self.ControlMainArm(self.param.coordinaLeft2, 1, self.param.waypointLeft2, self.param.velocityLeft2)
            ret = self.WaitContinuousMotionFinish(40)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True

        #--- turn off workend leveling
        self.feedback.substep = 7
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn off workend leveling." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.workendLeveling.value, SWITCH.off.value)
            ret = self.CheckBaseControlResult(CMD.workendLeveling.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False
            else:
                return True
    
    def StepExecDescend(self, substep):
        '''
        This function descends the arm to a constant-height pose according to laser aquisition.
        Finally, perform the workend leveling.
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 2
        self.feedback.step = STEP.descend.value

        #--- move two workend trans to middle
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Expand scanners for height detection." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            modeAbs = True
            self.ControlBase(CMD.workendTrans.value, SWITCH.on.value, modeAbs, 10, 10, 0, 0)
            ret = self.CheckBaseControlResult(CMD.workendTrans.value, 10)
            if(not ret):
                return False
            # #--- move scanner close to tile's edges and clear edge data obtrained from fetch
            # self.ControlBase(CMD.moveScanner.value, SWITCH.on.value, modeAbs, self.param.locaVelMove, self.param.locaVelMove, self._scanStartPosLon, self._scanStartPosLat)
            # ret = self.CheckBaseControlResult(CMD.moveScanner.value, 10)
            # if(not ret):
            #     return False
            # self._scanStartPosLon = 0.0
            # self._scanStartPosLat = 0.0
            if(substep == self.feedback.substep):
                return True

        #--- descend main arm to a moderate height for localization
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Descend main arm to a proper height for localization." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            modeInc = 0
            dx = self.param.globalTileLon - self._tileLenLon if(self._tileLenLon != 0) else 0
            dy = 0
            dz = 100
            da = 0
            self.ControlMainArmLinear(modeInc, dx, dy, dz, da, self.param.descendVel)
            print(pt.yellow_bold + "Adjust [%.1f]mm in X direction for different widths of sampled tile[%.1f] and current tile[%.1f]."
                  % (dx, self.param.globalTileLon, self._tileLenLon))
            print(pt.default + "Check height data of referred side: {}".format(self.param.locaRefSide))
            print(pt.default + "During descend step's descending, head height data [H/FL/FR/Side]", end=':', flush=True)
            timeStart = time.time()
            while(True):
                time.sleep(1)
                ret = self.CheckTimeoutAndPreempt(timeStart, 30, "descend to a moderate height for further localization")
                if(ret == RET.timeout.value):
                    return False
                elif(ret == RET.cancel.value):
                    ad.ContinuousMotionStop()
                    return False
                #--- search legal laser distance, if no, descend further; if yes, descend accurately to a calc. dist.
                # scannerHeightSide = self._scannerHeightLeft if('left' in self.param.locaRefSide) else self._scannerHeightRight
                # if( (self.param.scannerRangeMin < self._scannerHeightHead   < self.param.scannerRangeMax) \
                # and (self.param.scannerRangeMin < self._scannerHeightFLeft  < self.param.scannerRangeMax) \
                # and (self.param.scannerRangeMin < self._scannerHeightFRight < self.param.scannerRangeMax) \
                # and (self.param.scannerRangeMin < scannerHeightSide         < self.param.scannerRangeMax) ):
                #     rospy.loginfo(pt.green + "Height Head[%d] FL[%d] FR[%d] Side[%d] for localization arrival. Stop descending." % 
                #         (self._scannerHeightHead, self._scannerHeightFLeft, self._scannerHeightFRight, scannerHeightSide))
                if(self.param.scannerRangeMin < self._scannerHeightHead < self.param.scannerRangeMax):
                    rospy.loginfo(pt.green + "Height Head[%d] for localization arrival. Stop descending." % self._scannerHeightHead)
                    ad.ContinuousMotionStop()
                    print("---|")
                    break
                else:
                    print("%d" % self._scannerHeightHead,   end=' ',  flush=True)
                    # print("%d" % self._scannerHeightFLeft,  end=' ',  flush=True)
                    # print("%d" % self._scannerHeightFRight, end=' ',  flush=True)
                    # print("%d" % scannerHeightSide,         end=', ', flush=True)
            return True

    def StepExecDescend2(self, substep):
        '''
        This function descends the arm to a constant-height pose according to laser aquisition.
        Finally, perform the workend leveling.
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 1
        self.feedback.step = STEP.descend.value

        #--- descend main arm to a moderate height for localization
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Descend main arm to a proper height for localization." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)

            flag = True
            self._scannerHeightFLeft  = 0
            self._scannerHeightFRight = 0
            self._scannerHeightRight  = 0
            height = 0 # init. height of descending     #50
            typeHeight = 3
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeight)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 5)
            if(not ret):
                return False
            minHeight = self.param.fetchHeightSensorFixed-35      #35
            if(minHeight < self._scannerHeightFLeft < 1000):
                height = self._scannerHeightFLeft
                print("Select FL height [%.1f]mm for descending." % height)
            elif(minHeight < self._scannerHeightFRight < 1000):
                height = self._scannerHeightFRight
                print("Select FR height [%.1f]mm for descending." % height)
            elif(minHeight < self._scannerHeightRight < 1000):
                height = self._scannerHeightRight
                print("Select Right height [%.1f]mm for descending." % height)
            else:
                flag = False

            if(flag):
                # dx = (self.param.globalTileLon - self._tileLenLon)/2 if(self._tileLenLon != 0) else 0
                dx = (self.param.globalSampleTileLon - self.param.globalTileLon)/2
                dy = 0
                dz = height - self.param.fetchHeightSensorFixed-55+15+10 #- 52 # 52: thickness of the tile     fetchHeightSensorFixed是相机到吸盘距离
                da = 0
                print("[Decend] heights FL[%.1f]/FR[%.1f]/R[%.1f] range(%.0f, 1000)"
                    % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight, minHeight))
                print(pt.green + "Descending [%.1f]mm to a moderate height for localization." % dz)
                print(pt.yellow_bold + "Adjust [%.1f]mm in X direction for different widths (lon) of sampled tile[%.1f] and current tile[%.1f]."
                    % (dx, self.param.globalSampleTileLon, self.param.globalTileLon))
                # self.ControlMainArmLinear(modeInc, dx, dy, dz, da, self.param.velDescend)
                ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velDescend)
                if(not ret):
                    return False
            else:
                #--- perform a further descending if no scanner data were measured
                print(pt.yellow + "No legal height data found, and descending [%.1f]mm and detect height again." % height)
                moveArray = [0, 50, 0, 0]
                ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velDescend)
                if(not ret):
                    return False

                self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeight)
                ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 5)
                if(not ret):
                    return False
                print("[Decend] heights FL[%.1f]/FR[%.1f]/R[%.1f] range(%.0f, 1000)"
                    % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight, minHeight))
                if(minHeight < self._scannerHeightFLeft < 1000):
                    height = self._scannerHeightFLeft
                    print("Select FL height [%.1f]mm for descending." % height)
                elif(minHeight < self._scannerHeightFRight < 1000):
                    height = self._scannerHeightFRight
                    print("Select FR height [%.1f]mm for descending." % height)
                elif(minHeight < self._scannerHeightRight < 1000):
                    height = self._scannerHeightRight
                    print("Select Right height [%.1f]mm for descending." % height)
                else:
                    print(pt.red + "No legal height data obtained for the second time while descending! Abort.")
                    return False
                dx = (self.param.globalSampleTileLon - self.param.globalTileLon)/2
                dy = 0
                dz = height - self.param.fetchHeightSensorFixed-55+15+10 #- 42 # 42: thickness of the tile
                da = 0
                ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velDescend)
                if(not ret):
                    return False
                print(pt.yellow_bold + "Descend further [%.1f]mm." % dz)
                print(pt.yellow_bold + "Adjust [%.1f]mm in X direction for different widths (lon) of sampled tile[%.1f] and current tile[%.1f]."
                    % (dx, self.param.globalSampleTileLon, self.param.globalTileLon))
            #kun_test
            # #--- ajust workend tilt angle to align with ground tile for once
            # print(pt.default + "[Descend substep 2] workend leveling for once by scanners' data.")
            # typeLay = 6
            # self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeLay)
            # ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            # if(not ret):
            #     return False
            # deltaPitch = -self._pitch
            # deltaRoll  = -self._roll
            # modInc = 0
            # self.ControlBase(CMD.workendJoints.value, SWITCH.on.value, modInc, 1000, 1000, deltaPitch, deltaRoll)
            # print("Control pitch[%.1f]deg and roll[%.1f]deg for tilt alignment." % (deltaPitch, deltaRoll))

            #--- turn on the once workend leveling
            #kun_test
            print(pt.cyan + "[Descend substep 2] Turn on workend leveling for once.")
            quitLevelingAfterAchieve = True
            tolAngle  = int(self.param.tolAngleWorkendLeveling)
            goalRoll  = 0
            goalPitch = 0
            self.ControlBase(CMD.workendLeveling.value, SWITCH.on.value, quitLevelingAfterAchieve, tolAngle, goalRoll, goalPitch)
            ret = self.CheckBaseControlResult(CMD.workendLeveling.value, 40)
            if(not ret):
                return False
            #kun_test
            return True

    def StepExecMoveFront2(self, layside, daubHeight):
        '''
        This function executes the all substeps of STEP_daub and STEP_moveLeft/Middle/Right.
        And control workendleveling and turned it off at the end in the process.
        In addition, automatically generate the daub end line waypoint by taking daub height into account.
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 7
        self.feedback.step = STEP.moveFront.value

        # #--- turn on the continuous workend leveling
        # print(pt.default + "Got layside [{}].".format(layside))
        # print(pt.default + "Turn on workend leveling for first.")
        # quitLevelingAfterAchieve = False
        # tolAngle  = int(self.param.tolAngleWorkendLeveling)
        # goalRoll  = 0
        # goalPitch = 0
        # self.ControlBase(CMD.workendLeveling.value, SWITCH.on.value, quitLevelingAfterAchieve, tolAngle, goalRoll, goalPitch)

        #--- Recover workend joints to zero positions for daubing
        goalPitch = 0
        goalRoll  = 0
        modAbs = 1
        self.ControlBase(CMD.workendJoints.value, SWITCH.on.value, modAbs, 1000, 1000, goalPitch, goalRoll)

        #--- send and start continuous motion control
        waypointFront = []
        velocityFront = 0.0
        coordinaFront = 1
        if('right' in layside):
            waypointFront = self.param.waypointRight
            velocityFront = self.param.velocityRight
            coordinaFront = self.param.coordinaRight
        elif('middle' in layside):
            waypointFront = self.param.waypointMiddle
            velocityFront = self.param.velocityMiddle
            coordinaFront = self.param.coordinaMiddle
        elif('left' in layside):
            waypointFront = self.param.waypointLeft
            velocityFront = self.param.velocityLeft
            coordinaFront = self.param.coordinaLeft
        elif('left2' in layside):
            waypointFront = self.param.waypointLeft2
            velocityFront = self.param.velocityLeft2
            coordinaFront = self.param.coordinaLeft2
        else:
            print(pt.red + "mismatch arg. [{}]. It should be 'right'/'middle'/'left'/'left2'.".format(layside))
            return False

        waypointDaubStartXbias = self.param.waypointDaubStart.copy()
        waypointDaubEndTmp     = self.param.waypointDaubEnd.copy()
        # print("para:{} var_before:{} arg:{}".format(self.param.waypointDaubStart, waypointDaubStartTmp, daubHeight))
        marginLat = self.param.globalTileLat/2 if(self.param.globalFetchMarginLat == 0) else self.param.globalFetchMarginLat
        biasX = -(marginLat - self.param.globalSampleTileLat / 2.0)
        waypointDaubStartXbias[0] += biasX
        waypointDaubAttach     = waypointDaubStartXbias.copy()
        waypointDaubAttach[2] += daubHeight
        waypointDaubEndTmp[2] += daubHeight
        print(pt.yellow + "[Clean-Daub] bias [%.1f]mm in X dir. with sample tile lat [%.0f]mm and fetch margin lat[%.0f]mm"
            % (biasX, self.param.globalSampleTileLat, marginLat))

        arrayCoord = []
        arrayCoord.append(self.param.coordinaInitPos)
        arrayCoord.append(self.param.coordinaBackAvo)
        arrayCoord.append(self.param.coordinaDaubStart)
        arrayCoord.append(self.param.coordinaDaubStart)
        arrayCoord.append(self.param.coordinaDaubEnd)
        # arrayCoord.append(coordinaFront)
        arrayPos = []
        arrayPos.append(self.param.waypointInitPos)
        arrayPos.append(self.param.waypointBackAvo)
        arrayPos.append(waypointDaubStartXbias)
        arrayPos.append(waypointDaubAttach)
        arrayPos.append(waypointDaubEndTmp)
        # arrayPos.append(waypointFront)
        arrayVel = []
        arrayVel.append(self.param.velocityInitPos)
        arrayVel.append(self.param.velocityBackAvo)
        arrayVel.append(self.param.velocityDaubStart)
        arrayVel.append(self.param.velocityDaubStart)
        arrayVel.append(self.param.velocityDaubEnd)
        # arrayVel.append(velocityFront)
        ad.ContinuousMotionQueue(1, arrayCoord, arrayPos, arrayVel, 5, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)

        #--- check the arrival of the waypoints
        flag1stTrigger1 = False
        flag1stTrigger2 = False
        flag1stTrigger3 = False
        flag1stTrigger4 = False
        flag1stTrigger5 = False
        timeStart = time.time()
        print(pt.default + "restNode", end=': ', flush=True)
        while(True):
            time.sleep(0.5)
            ret = self.CheckTimeoutAndPreempt(timeStart, 120, "contiunous motion to front")
            if(ret == RET.timeout.value):
                return False
            elif(ret == RET.cancel.value):
                # ad.ServoPowerOff()
                ad.ContinuousMotionStop()
                return False

            #--- query the rest nodes in continuous motion control
            restNodes = ad.GetRestContinuousNodes()
            print(restNodes, end=',', flush=True)
            if((not flag1stTrigger1) and (restNodes == 5)):
                flag1stTrigger1 = True
                self.feedback.substep = 1
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: init avo & daub avoidance." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
            elif((not flag1stTrigger2) and (restNodes == 3)):
                flag1stTrigger2 = True
                self.feedback.substep = 2
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: daub above with x bias." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
            elif((not flag1stTrigger3) and (restNodes == 2)):
                flag1stTrigger3 = True
                self.feedback.substep = 3
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: daub start." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
            elif((not flag1stTrigger4) and (restNodes == 1)):
                flag1stTrigger4 = True
                self.feedback.substep = 4
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Checking workend joints arriave zero positions." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
                #--- send the last(5th) waypoint as in continuous motion mode [20231227]
                arrayCoord = []
                arrayCoord.append(coordinaFront)
                arrayPos = []
                arrayPos.append(waypointFront)
                arrayVel = []
                arrayVel.append(velocityFront)
                ad.ContinuousMotionQueue(0, arrayCoord, arrayPos, arrayVel, 1, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)
                #--- check workend joints are at zero positions
                if(self._baseStatus[CMD.workendJoints.value] != REPLY.succeeded.value):
                    time.sleep(10)
                    if(self._baseStatus[CMD.workendJoints.value] != REPLY.succeeded.value):
                        print(pt.red+"Failed to control workend joints to be at zero positions! Abort.")
                        return False
                else:
                    print(pt.green+"Workend joints are at zero positions. Do not stop to wait.")
                # #--- check workend leveling when the arm is at the waypoint of daub start
                # if(self._baseStatus[CMD.workendLeveling.value] != REPLY.succeeded.value):
                #     ad.ContinuousMotionSuspend()
                #     print(pt.yellow + "[CONTINUOUS MOTION] Workend leveling is not achieved! Wait 60s...")
                #     timeStart2 = time.time()
                #     while(True):
                #         ret = self.CheckTimeoutAndPreempt(timeStart2, 60, "Await workend leveling ends for 60s 1/2")
                #         if(ret == RET.timeout.value):
                #             return False
                #         elif(ret == RET.cancel.value):
                #             ad.ContinuousMotionStop()
                #             # ad.ServoPowerOff()
                #             return False
                        
                #         if(self._baseStatus[CMD.workendLeveling.value] == REPLY.succeeded.value):
                #             print(pt.green + "[CONTINUOUS MOTION] workend leveling has been achieved (%ds)! Continue." % (time.time() - timeStart2))
                #             self._baseStatus[CMD.workendLeveling.value] = REPLY.null.value
                #             ad.ContinuousMotionStart()
                #             break
                # else:
                #     print(pt.green + "[CONTINUOUS MOTION] Workend leveling is achieved! Do not stop to wait.")
                self.feedback.substep = 5
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: daub end." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
            elif((not flag1stTrigger5) and (restNodes == 1)):
                flag1stTrigger5 = True
                self.feedback.substep = 6
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: front %s" % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps, layside))
                self.actionServer.publish_feedback(self.feedback)
            elif(restNodes == 0):
                print(pt.green + "All waypoints arrival.")
                self.feedback.substep = 7
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> All waypoints to front have been covered." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
                return True
                # self._baseStatus[CMD.workendLeveling.value] = REPLY.null.value
                # #--- turn off workend leveling when satisfy leveling condition after the arm finishes continuous motion control
                # timeStart3 = time.time()
                # while(True):
                #     ret = self.CheckTimeoutAndPreempt(timeStart3, 60, "Await workend leveling ends for 60s 2/2")
                #     if(ret == RET.timeout.value):
                #         return False
                #     elif(ret == RET.cancel.value):
                #         ad.ContinuousMotionStop()
                #         ad.ServoPowerOff()
                #         return False
                #     if(self._baseStatus[CMD.workendLeveling.value] == REPLY.succeeded.value):
                #         print(pt.green + "[CONTINUOUS MOTION] workend leveling finished after continuous motion has been achieved in (%d)s !" % (time.time() - timeStart3))
                #         self.ControlBase(CMD.workendLeveling.value, SWITCH.off.value)
                #         ret = self.CheckBaseControlResult(CMD.workendLeveling.value, self.param.timeoutControlBaseReply)
                #         if(not ret):
                #             return False
                #         else:
                #             return True


    def StepExecMoveRear2(self):
        '''
        This function makes the arm walk through all waypoints to the rear of the robot in continous mode.
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 3
        self.feedback.step = STEP.moveRear2.value

        #--- shrink all scanners
        # modeAbs = True
        # self.ControlBase(CMD.moveScanner.value, SWITCH.on.value, modeAbs, self.param.locaVelMove, self.param.locaVelMove, 0,0)

        #--- send and start continuous motion control
        arrayCoord = []
        arrayCoord.append(self.param.coordinaDaubEnd)
        arrayCoord.append(self.param.coordinaBackAvo)
        # arrayCoord.append(self.param.coordinaInitPos)
        arrayPos = []
        arrayPos.append(self.param.waypointDaubEnd)
        arrayPos.append(self.param.waypointBackAvo)
        # arrayPos.append(self.param.waypointInitPos)
        arrayVel = []
        arrayVel.append(self.param.velMoverearToDaubEnd)
        arrayVel.append(self.param.velMoverearToBackAvo)
        # arrayVel.append(self.param.velMoverearToInitPos)
        ad.ContinuousMotionQueue(1, arrayCoord, arrayPos, arrayVel, 2, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)
        
        #--- check the arrival of the waypoints
        flag1stTrigger1 = False
        flag1stTrigger2 = False
        flag1stTrigger3 = False
        timeStart = time.time()
        print(pt.default + "Rest nodes on the way back", end=': ', flush=True)
        while(True):
            time.sleep(0.5)
            ret = self.CheckTimeoutAndPreempt(timeStart, 120, "continuous motion to rear")
            if(ret == RET.timeout.value):
                return False
            elif(ret == RET.cancel.value):
                ad.ContinuousMotionStop()
                ad.ServoPowerOff()
                return False
            
            #--- acquire the rest nodes in continuous motion control
            restNodes = ad.GetRestContinuousNodes()
            print(restNodes, end=',', flush=True)
            if((not flag1stTrigger1) and (restNodes == 2)):
                flag1stTrigger1 = True
                self.feedback.substep = 1
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: front avoidance." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
            elif((not flag1stTrigger2) and (restNodes == 1)):
                flag1stTrigger2 = True
                self.feedback.substep = 2
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: back avoidance." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
                #--- send the last(3rd) waypoint as in continuous motion mode [20231227]
                arrayCoord = []
                arrayCoord.append(self.param.coordinaInitPos)
                arrayPos = []
                arrayPos.append(self.param.waypointInitPos)
                arrayVel = []
                arrayVel.append(self.param.velMoverearToInitPos)
                ad.ContinuousMotionQueue(0, arrayCoord, arrayPos, arrayVel, 1, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)
            elif((not flag1stTrigger3) and (restNodes == 1)):
                flag1stTrigger3 = True
                self.feedback.substep = 3
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Approaching waypoint: back destination." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
                self.actionServer.publish_feedback(self.feedback)
            elif(restNodes == 0):
                print(pt.green + "All waypoints arrival.")
                break
        # ret = self.CheckBaseControlResult(CMD.moveScanner.value, 5)
        # if(not ret):
        #     return False
        # else:
        #     return True
        return True


    def StepExecStepback(self, substep):
        '''
        This function can execute all substeps in one-time calling (set SUBSTEP to 0),
        As well as execute one single substep (set SUBSTEP to 1~6).
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 6
        self.feedback.step = STEP.stepback.value

        #--- turn on paver deliver
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> turn on paver deliver." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.paverDeliver.value, SWITCH.on.value, self.param.velPaverDeliver)
            ret = self.CheckBaseControlRec(CMD.paverDeliver.value, 2)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- turn paver leveling on
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn on paver leveling." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            tolDist = int(self.param.tolDistPaverLeveling) # unit:mm
            self.ControlBase(CMD.paverLeveling.value, SWITCH.on.value, 0, 0, tolDist)
            ret = self.CheckBaseControlRec(CMD.paverLeveling.value, 2)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- move front scanners to detect ground tile's edge
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> move arm for edge detection." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # move a distance left based on the waypoint left to prevent blocking the laser line for paver leveling 
            waypointLeft = self.param.waypointLeft.copy()
            waypointLeft[0] += self.param.stepbackArmShiftDistX   # got larger range compared to adjust scan detect point
            waypointLeft[1] += self.param.stepbackArmShiftDistY
            #kun_test
            self.ControlMainArm(self.param.coordinaRight,1,self.param.waypointRight,self.param.velocityRight)
            self.WaitContinuousMotionFinish(40)
            #kun_test
            # if(not ret):
            #     return False
            #kun_test
            #--- query FL & FR height data
            self._scannerHeightFLeft  = 0
            self._scannerHeightFRight = 0
            typeHeightMiddle = 4
            # self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMiddle, self.param.locaLonDetect)
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMiddle, 1500)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            if(not ret):
                return False
            
            #--- descend 50mm if no legal height data obtained
            if(self._scannerHeightFLeft == 0 or self._scannerHeightFRight == 0):
                print(pt.yellow+"Some scanner height out of range: FL[%.0f] or FR[%.1f]mm, descend 50mm." % (self._scannerHeightFLeft, self._scannerHeightFRight))
                moveArray = [0, 50, 0, 0]
                ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velDescend)
                if(not ret):
                    return False
                self._scannerHeightFLeft  = 0
                self._scannerHeightFRight = 0
                typeHeightMiddle = 4
                # self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMiddle, self.param.locaLonDetect)
                self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMiddle, 1500)
                ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
                if(not ret):
                    return False
                if(self._scannerHeightFLeft == 0 and self._scannerHeightFRight == 0):
                    print(pt.red+"No legal height data obtained after 50mm descending. Abort.")
                    return False
                else:
                    heightAvailable = self._scannerHeightFLeft if(0 < self._scannerHeightFLeft < 1000) else self._scannerHeightFRight
                    descendHeight = heightAvailable - self.param.fetchHeightSensorFixed - 40
                    moveArray = [0, descendHeight, 0, 0]
                    print("Descend [%.0f] mm to a moderate height (current height: [%.0f] fixSensor [%.0f] and gap [40]mm)." 
                        % (descendHeight, self.param.fetchHeightSensorFixed, heightAvailable))
                    ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velDescend)
                    if(not ret):
                        return False
            else:
                heightAvailable = self._scannerHeightFLeft if(0 < self._scannerHeightFLeft < 1000) else self._scannerHeightFRight
                descendHeight = heightAvailable - self.param.fetchHeightSensorFixed - 40
                moveArray = [0, descendHeight, 0, 0]
                print("Descend [%.0f] mm to a moderate height with available height [%.1f]FL/FR(%.1f,%.1f) and fixed height [%.1f]+40." 
                      % (descendHeight, heightAvailable, self._scannerHeightFLeft, self._scannerHeightFRight, self.param.fetchHeightSensorFixed))
                ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velDescend)
                if(not ret):
                    return False
            if(substep == self.feedback.substep):
                return True

        #--- perform base stepback movement
        self.feedback.substep = 4
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Base step back." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            
            #--- track laser line backward
            print(pt.default + "[STEPBACK] Move back with manual setting max distance: %d mm" % self.param.stepbackMaxDistance)
            self.ControlBase(CMD.trackLine.value, SWITCH.on.value, self.param.velStepback, self.param.stepbackMaxDistance) # vel, pos

            heightFL_prev = self._scannerHeightFLeft
            heightFR_prev = self._scannerHeightFRight
            timeStart = time.time()
            print("scan data of FL/FR while stepping back:")
            while(True):
                time.sleep(1)
                ret = self.CheckTimeoutAndPreempt(timeStart, 120, "check ground tile's edge while stepping back")
                if(ret == RET.timeout.value):
                    return False
                elif(ret == RET.cancel.value):
                    self.ControlBase(CMD.trackLine.value, SWITCH.off.value)
                    return False

                self._scannerHeightFLeft  = 0
                self._scannerHeightFRight = 0
                typeHeightMiddle = 4
                # self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMiddle, self.param.locaLonDetect)
                self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMiddle, 1500)
                ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 1)
                if(not ret):
                    return False
                
                print("%d,%d" % (self._scannerHeightFLeft, self._scannerHeightFRight),  end='|',  flush=True)
                if(((self._scannerHeightFRight - heightFR_prev > 15) and (15<self._scannerHeightFRight<1000) and (15<heightFR_prev<1000))
                 or ((self._scannerHeightFLeft - heightFL_prev > 15) and (15<self._scannerHeightFLeft <1000) and (15<heightFL_prev<1000))  ):
                    print("---|")
                    print(pt.default + " ")
                    print(pt.green_bold + "Detect the ground tile's edge [%.1f-->%.1f](FL) or [%.1f-->%.1f](FR) diff:[%.1f] thres: 15. Stop stepping back with speed [%.0f]mm/s in [%.1f]s." 
                        % (heightFL_prev, self._scannerHeightFLeft, heightFR_prev, self._scannerHeightFRight, 
                           self._scannerHeightFRight - heightFR_prev, self.param.velStepback, time.time()-timeStart))
                    print(pt.default + " ")
                    print(pt.yellow_bold + "=============== Next line operations starts here ===================")
                    print(pt.default + " ")
                    self.ControlBase(CMD.trackLine.value, SWITCH.off.value)
                    break
                heightFL_prev = self._scannerHeightFLeft if(15<self._scannerHeightFLeft<1000) else heightFL_prev
                heightFR_prev = self._scannerHeightFRight if(15<self._scannerHeightFRight<1000) else heightFR_prev
            if(substep == self.feedback.substep):
                return True

        #--- turn off paver deliver
        self.feedback.substep = 5
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> turn off paver deliver." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.paverDeliver.value, SWITCH.off.value)
            ret = self.CheckBaseControlResult(CMD.paverDeliver.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- turn off the paver leveling
        self.feedback.substep = 6
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn off paver leveling." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # time.sleep(5)
            time.sleep(1) 
            self.ControlBase(CMD.paverLeveling.value, SWITCH.off.value)
            ret = self.CheckBaseControlRec(CMD.paverLeveling.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False
            else:
                return True

    def StepExecFetch(self, substep):
        self.feedback.steps = 1
        self.feedback.substeps = 7
        self.feedback.step = STEP.fetch.value

        #--- workend leveling for once
        #--- workend leveling to ajdust pose in advance
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Workend leveling for once." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            quitLevelingAfterAchieve = True
            tolAngle = int(self.param.tolAngleWorkendLeveling)
            self.ControlBase(CMD.workendLeveling.value, SWITCH.on.value, quitLevelingAfterAchieve, tolAngle)
            ret = self.CheckBaseControlResult(CMD.workendLeveling.value, self.param.timeoutWorkendLeveling)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True

        #--- descend main arm to a moderate height
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Descend main arm & shrink scanner & workendtrans to middle." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)

            #--- shrink all scanners and move both workend trans to middle
            modeAbs = True
            self.ControlBase(CMD.moveScanner.value, SWITCH.on.value, modeAbs, self.param.locaVelMove, self.param.locaVelMove, 0, 0)            
            self.ControlBase(CMD.workendTrans.value, SWITCH.on.value, modeAbs, 10,10, 0,0)
            
            #--- descend and check heights along the way
            modeInc = 0
            dx = 0
            dy = 0
            dz = 200
            da = 0
            self.ControlMainArmLinear(modeInc, dx, dy, dz, da, self.param.velFetchDown)
            print(pt.default + "During fetch descending, head height data (FL/FR/L/R)", end=':', flush=True)
            timeStart = time.time()
            while(True):
                time.sleep(1)
                ret = self.CheckTimeoutAndPreempt(timeStart, 40, "descend to a height for localization")
                if(ret == RET.timeout.value):
                    return False
                elif(ret == RET.cancel.value):
                    ad.ContinuousMotionStop()
                    return False
                #--- search legal laser distance, if no, descend further; if yes, descend accurately to a calc. dist.
                if( (self.param.scannerRangeMin < self._scannerHeightFLeft  < self.param.fetchHeightAlign) \
                and (self.param.scannerRangeMin < self._scannerHeightFRight < self.param.fetchHeightAlign) \
                and (self.param.scannerRangeMin < self._scannerHeightLeft   < self.param.fetchHeightAlign) \
                and (self.param.scannerRangeMin < self._scannerHeightRight  < self.param.fetchHeightAlign) ):
                    ad.ContinuousMotionStop()
                    rospy.loginfo(pt.green + "scanner heights FL[%d]FR[%d]L[%d]R[%d] for align arrival in fetch. Stop descending." \
                                  % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightLeft, self._scannerHeightRight))
                    print("---|")
                    break
                else:
                    print("%d" % self._scannerHeightFLeft,  end=' ',  flush=True)
                    print("%d" % self._scannerHeightFRight, end=' ',  flush=True)
                    print("%d" % self._scannerHeightLeft,   end=' ',  flush=True)
                    print("%d" % self._scannerHeightRight,  end=', ', flush=True)
            
            #--- check execution results of shrinking scanners and bringing both workend trans. to middle
            ret = self.CheckBaseControlResult(CMD.moveScanner.value, 10)
            if(not ret):
                return False
            ret = self.CheckBaseControlResult(CMD.workendTrans.value, 10)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True
            
        #--- workend positioning, and move to center of the upper tile with right angle
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> workend positioning & pose alignment." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            # move arm to align with the top tile in x and y directions
            scanType = 4 # lon and lat scanning for fetching tiles
            self.ScanLocalization(CMD.scanLoca.value,
                                  self.param.globalTileLat, 
                                  self.param.globalTileLon,
                                  scanType,
                                  self.param.locaVelMove,
                                  self.param.locaVelScan,
                                  0,
                                  self.param.locaLonEnd,
                                  0,
                                  self.param.locaLatEnd)
            ret = self.CheckBaseControlResult(CMD.scanLoca.value, 100)
            if(not ret):
                return False
            # move trans to align to the tile
            if(self.WORKEND_TRANS_INSTALLED == 1):
                print("--- Use workend translational mechanism to align to top tile ---")
                moveA = self._biasA + self.param.fetchLocaOffsetA
                moveX = self._biasX + self.param.fetchLocaOffsetY
                moveY = self._biasY - self.param.fetchLocaOffsetX
                modeInc = False
                self.ControlBase(CMD.workendTrans.value, SWITCH.on.value, modeInc, 5,5, int(moveX), int(moveY))
                ret1 = self.ControlMainArmUntilFinishLinear(MOD.inc.value, 0, 0, 0, moveA, 10)
                ret2 = self.CheckBaseControlResult(CMD.workendTrans.value, 20)
                if((not ret1) or (not ret2)):
                    return False
            else:
                print("--- Use robot arm joint to adjust lay angle (or pose) ---")
                moveA =  self._biasA + self.param.fetchLocaOffsetA
                moveX = -self._biasY + self.param.fetchLocaOffsetX
                moveY =  self._biasX + self.param.fetchLocaOffsetY
                ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, moveX, moveY, 0, moveA, 10)
                if(not ret):
                    return False
            if((self._toEdgeLon == 0.0) or (self._toEdgeLat == 0.0)):
                print(pt.yellow_bold + "No tile's edge data recorded, ignore close-to-edge movement control.")
                self._scanStartPosLon = 0
                self._scanStartPosLat = 0
            else:
                self._scanStartPosLon = self._toEdgeLon - self.param.fetchScannerToEdgeMargin
                self._scanStartPosLat = self._toEdgeLat - self.param.fetchScannerToEdgeMargin
                if((self._scanStartPosLon < 0) or (self._scanStartPosLat < 0)):
                    print(pt.yellow + "Inappropriate margin set for scanner zero pos. to tile's edges: lon[%.1f] lat[%.1f] set margin[%.1f]"
                        % (self._toEdgeLon, self._toEdgeLat, self.param.fetchScannerToEdgeMargin))
                    print("Will set margin as minimum: use zero positions of canners as margin.")
                    self._scanStartPosLon = 0
                    self._scanStartPosLat = 0
            print(pt.yellow + "Record start positions of scanner in LON [%.1f] and LAT[%.1f]" % (self._scanStartPosLon, self._scanStartPosLat))
            if(substep == self.feedback.substep):
                return True
        
        #--- workend tilt aligment, in order to parallel to tiles, and get attached to tiles
        self.feedback.substep = 4
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> workend tilt angle alignment & attach." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            #--- shrink scanners
            modeAbs = True
            self.ControlBase(CMD.moveScanner.value, SWITCH.on.value, modeAbs, self.param.locaVelMove, self.param.locaVelMove, 0, 0)            
            ret = self.CheckBaseControlResult(CMD.moveScanner.value, 20)
            if(not ret):
                return False
            #--- check all data of scanners
            maxHeight = max(self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRear, self._scannerHeightLeft, self._scannerHeightRight)
            minHeight = min(self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRear, self._scannerHeightLeft, self._scannerHeightRight)
            if(maxHeight - minHeight > 30):
                print(pt.red + "Not all scanners above the tile! FL/FR/Rear/L/R [%.1f,%.1f,%.1f,%.1f,%.1f]"
                      % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRear, self._scannerHeightLeft, self._scannerHeightRight))
                time.sleep(2)
                maxHeight = max(self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRear, self._scannerHeightLeft, self._scannerHeightRight)
                minHeight = min(self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRear, self._scannerHeightLeft, self._scannerHeightRight)
                if(maxHeight - minHeight > 30):
                    print(pt.red + "Again(2s later): Not all scanners above the tile! FL/FR/Rear/L/R [%.1f,%.1f,%.1f,%.1f,%.1f]"
                        % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRear, self._scannerHeightLeft, self._scannerHeightRight))
                    print(pt.red + "Workend tilt alignment aborted!")
                    return False
            #--- execute tilt aligement
            self.ControlBase(CMD.workendAlignTilt.value, SWITCH.on.value, self.param.tolAngleTiltAlign)
            ret = self.CheckBaseControlResult(CMD.workendAlignTilt.value, self.param.timeoutWorkendLeveling)
            if(not ret):
                return False
            #--- descend to attach tile
            if(self.param.scannerRangeMin < self._scannerHeightRear < self.param.scannerRangeMax):
                rospy.loginfo("Current rear scanner height data [%.1f]mm" % self._scannerHeightRear)
            else:
                rospy.logerr("Rear scanner height data [%.1f] mm is out of range [%.1f, %.1f]!" % 
                    (self._scannerHeightRear, self.param.scannerRangeMin, self.param.scannerRangeMax) ) 
                return False
            moveZ = self._scannerHeightRear - self.param.fetchHeightSensorFixed
            moveArray= [0, moveZ, 0, 0]
            rospy.logwarn("Descend [%d] mm to get attached to the tiles." % moveZ)
            ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchAttach) 
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- turn on the pump
        self.feedback.substep = 5
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn on suck." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.suck.value, SWITCH.on.value)
            ret = self.CheckBaseControlRec(CMD.suck.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- press down and check pressure value
        self.feedback.substep = 6
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Press down while checking pressure." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            timeStart = time.time()
            pressTimes = 0
            while(True):
                time.sleep(2)
                ret = self.CheckTimeoutAndPreempt(timeStart, 30, "checking air pressure when press and fetch")
                if(ret == RET.timeout.value):
                    return False
                elif(ret == RET.cancel.value):
                    ad.ContinuousMotionStop()
                    return False
                rospy.logwarn("Current pressure: [%d]" % self._pressure)
                if(self._pressure > self.param.fetchPressureGrasp):
                    rospy.loginfo(pt.green_bold+"[Fetch] Succeed in fetching tiles with air pressure [%d]." % self._pressure)
                    break
                if(pressTimes < 3):
                    pressTimes += 1
                    moveArray = [0, 1, 0, 0]
                    ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchAttach)
                    if(not ret):
                        return False
                else:
                    print(pt.yellow + "Press down times is more than 3 times, stop pressing down and wait pressure check." + pt.default)
            if(substep == self.feedback.substep):
                return True

        #--- lift the tiles above
        self.feedback.substep = 7
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Lift tiles." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            moveArray = [0, -20, 0, 0]
            ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, 20)
            if(not ret):
                return False
            #--- move workend tans. back to middle points
            modeAbs = True
            self.ControlBase(CMD.workendTrans.value, SWITCH.on.value, modeAbs, 8,8, 0,0)
            ret = self.CheckBaseControlResult(CMD.workendTrans.value, 20)
            if(not ret):
                return False
            else:
                return True

    def StepExecFetch2(self, substep):
        '''
        This function utilizes scanner products for positioning, and hence shrink steps from 7 to 4, compressing the first 4 into 1.
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 4
        self.feedback.step = STEP.fetch.value

        heightGap = 28  #43
        #--- [1] horizontal pose alignment
        #--- [2] tilt pose (pitch & roll) alignment
        #--- [3] height alignment
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Workend alignment for fetch." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
        
            #--- check moderate height for further fetch
            print(pt.white_bold + "[fetch] STEP0/4: Decend a moderate height to form a gap [%d]" % heightGap)
            eachTimeDescendHeight = 50
            maxDescendTimes = 4
            descendTimes = 0
            flagHit = False
            for i in range(maxDescendTimes):
                self._scannerHeightFLeft  = 0
                self._scannerHeightFRight = 0
                self._scannerHeightRight  = 0

                typeHeightMostInside = 5
                self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMostInside)
                ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
                if(not ret):
                    return False
                
                if( (0 < self._scannerHeightFLeft  < 1000) 
                and (0 < self._scannerHeightFRight < 1000)
                and (0 < self._scannerHeightRight  < 1000)  ):
                    flagHit = True
                    minHeight = min(self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight)
                    dHeight = minHeight - self.param.fetchHeightSensorFixed - heightGap
                    moveArray = [0, dHeight, 0, 0]
                    ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchDown)
                    if(not ret):
                        return False
                    print("Got all three illegal heights of scanners: FL[%.0f] FR[%.0f] R[%.0f], use min one [%.0f] and descend [%.0f] to form gap [%.0f]mm"
                        % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight, minHeight, dHeight, heightGap))
                    break
                else:
                    print(pt.yellow+"Failed to get moderate height for fetch: FL[%.0f] FR[%.0f] R[%.0f], descend further 20 mm for (%d) times."
                        % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight, descendTimes))
                    moveArray = [0, eachTimeDescendHeight, 0, 0]
                    ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchDown)
                    if(not ret):
                        return False
            if(not flagHit):
                print(pt.red+ "Reach max times of descending: [%d], Abort." % maxDescendTimes)
                return False

            #--- fetch mode 2: three-steps fetching
            #--- adjust main arm: position, angle, and height
            #--- send localization commands
            typeFetch = 1
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeFetch, 0, self.param.globalTileLon, self.param.globalTileLat)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            if(not ret):
                return False
            
            dx = -self._biasX
            dy =  self._biasY
            dz =  self._biasZ - self.param.fetchHeightSensorFixed - heightGap
            da =  self._biasA
            self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velFetchAttach)
            print(pt.white_bold + "[fetch] STEP1/4: pose adjustment with offsets: dx[%.1f] dy[%.1f] da[%.1f] dz[%.1f]" % (dx, dy, da, dz))

            print(pt.white_bold + "[fetch] STEP2/4: ignore tilt adjustment.")
            # #--- adjust workend tilt alignment: pitch and roll
            # times = 4
            # flagHit = False
            # print(pt.white_bold + "[fetch] STEP2/4: tilt adjustment with {} times.".format(times))
            # for i in range(times):
            #     typeFetch = 1
            #     self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeFetch, 0, self.param.globalTileLon, self.param.globalTileLat)
            #     ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            #     if(not ret):
            #         return False
                
            #     print(pt.default + "[fetch] Tilt angles: pitch[%.0f]mdeg roll[%.0f]mdeg" % (self._pitch, self._roll))
            #     if(abs(self._pitch) < 300 and abs(self._roll) < 300):
            #         print(pt.green_bold + "[fetch] Tilt angles have been adjusted.")
            #         flagHit = True
            #         break
                
            #     modInc = 0
            #     deltaPitch = -self._pitch
            #     deltaRoll  = -self._roll 
            #     self.ControlBase(CMD.workendJoints.value, SWITCH.on.value, modInc, 1000, 1000, deltaPitch, deltaRoll)
            #     time.sleep(5)
            #     # ret = self.CheckBaseControlResult(CMD.workendJoints.value, 10)
            #     # if(not ret):
            #     #     return False
            # if(not flagHit):
            #     print(pt.red_bold + "Failed to adjust tilt angle(s)!")
            #     return False
            
            #--- Again, adjust main arm: position, angle, and height
            typeFetch = 1
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeFetch, 0, self.param.globalTileLon, self.param.globalTileLat)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            if(not ret):
                return False
            
            dx = -self._biasX
            dy =  self._biasY
            dz =  self._biasZ - self.param.fetchHeightSensorFixed - heightGap
            da =  self._biasA
            self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velFetchAttach)
            print(pt.white_bold + "[fetch] STEP3/4: again pose adjustment with offsets: dx[%.1f] dy[%.1f] da[%.1f] dz[%.1f]" % (dx, dy, da, dz))

            #--- check the results after alignment
            typeFetch = 1
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeFetch, 0, self.param.globalTileLon, self.param.globalTileLat)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            if(not ret):
                return False
            
            dz =  self._biasZ - self.param.fetchHeightSensorFixed - heightGap
            print(pt.white_bold + "[fetch] STEP4/4: check: dx[%.1f](2) dy[%.1f](2) da[%.1f](0.3) dz[%.1f](2) | pitch[%.1f](300) roll[%.1f](500)"
                  % (self._biasX, self._biasY, self._biasA, dz, self._pitch, self._roll))
            # if(abs(self._biasX) < 3 and abs(self._biasY) < 3 and abs(self._biasA) < 0.4 and abs(dz) < 2 and abs(self._pitch) < 300 and abs(self._roll) < 500):
            if(abs(self._biasX) < 3 and abs(self._biasY) < 3 and abs(self._biasA) < 0.4 and abs(dz) < 2):
                print(pt.green_bold+"[fetch] alignment PASS.")
            else:
                print(pt.red_bold+"[fetch] alignment FAILED.")
                return False
            
            #--- adjust fetch point according to margin parameters if they are non-zeros
            print(pt.yellow + "Adjust fetch point according to margin parameters if they are non-zero values: margin lon/lat[%.0f/%.0f]mm." 
                    % (self.param.globalFetchMarginLon, self.param.globalFetchMarginLat))
            if(self.param.globalFetchMarginLon != 0 or self.param.globalFetchMarginLat != 0):
                dx = -(self.param.globalTileLon / 2.0 - self.param.globalFetchMarginLon)
                dy =  (self.param.globalTileLat / 2.0 - self.param.globalFetchMarginLat)
                dz = 0
                da = 0
                self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velFetchAttach)
            else:
                print(pt.yellow + "Both margin settings are zeros. DO NOT adjust fetch point.")
            if(substep == self.feedback.substep):
                return True

        #--- turn on the pump
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn on suck." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.suck.value, SWITCH.on.value)
            ret = self.CheckBaseControlRec(CMD.suck.value, self.param.timeoutControlBaseReply+10)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- press down and check pressure value
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Descend 10mm and press down while checking pressure." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)

            moveArray = [0, heightGap, 0, 0]
            ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchDown)
            if(not ret):
                return False

            timeStart = time.time()
            pressTimes = 0
            while(True):
                time.sleep(2)
                ret = self.CheckTimeoutAndPreempt(timeStart, 30, "checking air pressure when press and fetch")
                if(ret == RET.timeout.value):
                    return False
                elif(ret == RET.cancel.value):
                    ad.ContinuousMotionStop()
                    return False
                
                rospy.logwarn("Current pressure: [%d]" % self._pressure)
                if(self._pressure > self.param.fetchPressureGrasp):
                    rospy.loginfo(pt.green_bold+"[Fetch] Succeed in fetching tiles with air pressure [%d]." % self._pressure)
                    break
                if(pressTimes < 3):
                    pressTimes += 1
                    moveArray = [0, 1, 0, 0]
                    ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchAttach)
                    if(not ret):
                        return False
                else:
                    print(pt.yellow + "Press down times is more than 3 times, stop pressing down and wait pressure check." + pt.default)
            if(substep == self.feedback.substep):
                return True

        #--- lift the tiles above
        self.feedback.substep = 4
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Lift tiles." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            if(substep != 0):
                print("Speed up: ignore lift after fetching when in loop mode or single substep mode.")
                #kun_test
                self.ControlMainArm(self.param.coordinaInitPos,1,self.param.waypointInitPos,self.param.velMoverearToInitPos)
                self.WaitContinuousMotionFinish(40)
                #kun_test
                return True
            moveArray = [0, -20, 0, 0]
            ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchDown)
            if(not ret):
                return False
            else:
                return True


    def StepExecLay(self, substep):
        self.feedback.steps = 1
        self.feedback.substeps = 6
        self.feedback.step = STEP.lay.value

        #--- main arm descends vertically to touch the ground
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Descend main arm to touch ground." % (TransEnumStep(self.feedback.step), self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)

            flag = True
            self._scannerHeightFLeft  = 0
            self._scannerHeightFRight = 0
            self._scannerHeightRight  = 0
            height = 100 # init. height of descending
            typeHeightMostOut = 3
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeHeightMostOut)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 5)
            if(not ret):
                return False
            if((self._scannerHeightFLeft <= self._scannerHeightFRight <= self._scannerHeightRight)
             or(self._scannerHeightRight <= self._scannerHeightFRight <= self._scannerHeightFLeft)):
                height = self._scannerHeightFRight
            elif((self._scannerHeightFRight <= self._scannerHeightFLeft <= self._scannerHeightRight)
             or(self._scannerHeightRight <= self._scannerHeightFLeft <= self._scannerHeightFRight)):
                height = self._scannerHeightFLeft
            elif((self._scannerHeightFRight <= self._scannerHeightRight <= self._scannerHeightFLeft)
             or(self._scannerHeightFLeft <= self._scannerHeightRight <= self._scannerHeightFRight)):
                height = self._scannerHeightRight
            else:
                print(pt.red + "No legal heights found: FL[%.1f]/FR[%.1f]/R[%.1f]"
                    % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight))
                return False
            
            rospy.loginfo(pt.default + "Got current height(FL/FR/R) [%.0f, %.0f, %.0f]mm, fixed height[%.0f]mm" % 
                (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight, self.param.fetchHeightSensorFixed))
            moveZ = height - self.param.fetchHeightSensorFixed
            print("Got scanner heights FL/FR/R [%.1f, %.1f, %.1f] and choose the middle one [%.1f] for descending [%.1f]mm and touching ground. Fixed height [%.1f]mm"
                % (self._scannerHeightFLeft, self._scannerHeightFRight, self._scannerHeightRight, height, moveZ, self.param.fetchHeightSensorFixed))
            ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, 0, 0, moveZ, 0, self.param.velLayAttach)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- turn off the sucker
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn off sucker." % (TransEnumStep(self.feedback.step), self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.suck.value, SWITCH.stop.value)
            ret = self.CheckBaseControlResult(CMD.suck.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True
            
            # self.ControlBase(CMD.suck.value, SWITCH.release.value)
            # ret = self.CheckBaseControlResult(CMD.suck.value, self.param.timeoutControlBaseReply)
            # if(not ret):
            #     return False
            # elif(substep == self.feedback.substep):
            #     return True

        #--- check air pressure
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Check air pressure." % (TransEnumStep(self.feedback.step), self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            print(pt.default + "[lay] Air pressure record", end=': ', flush=True)
            timeStart = time.time()
            while True:
                time.sleep(1)
                if(time.time() - timeStart > 20):
                    rospy.logerr("[lay] Failed to release with final air presure [%d]" % self._pressure)
                    return False
                if(self._pressure < self.param.layPressureLoose):
                    print("---|")
                    rospy.loginfo("[lay] Succeed in releasing tiles.")
                    break
                else:
                    print(self._pressure, end=',', flush=True)
            if(substep == self.feedback.substep):
                return True
        
        #--- vibration adjustment
        self.feedback.substep = 4
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Vibration adjustment." % (TransEnumStep(self.feedback.step), self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)

            print("TODO: surface adjumstment via vibration.")

            # press down the tile further
            moveZ = 10
            print("Press down a further [%d] mm to attach the tile(s) tightly." % moveZ)
            self.ControlMainArmLinear(MOD.inc.value, 0, 0, moveZ, 0, 20)
            
            if(self.param.layTimeVibrate < 0.1):
                print("Vibration has been ignored due to parameter set to 0.")
                return True
            
            timeStart = time.time()
            self.ControlBase(CMD.vibrate.value, SWITCH.on.value)
            while(True):
                time.sleep(0.5)
                ret = self.CheckTimeoutAndPreempt(timeStart, 10, "time of vibration.")
                if(ret == RET.timeout.value):
                    return False
                elif(ret == RET.cancel.value):
                    return False
                if(time.time() - timeStart > self.param.layTimeVibrate):
                    print("Stop vibrating in %f seconds." % self.param.layTimeVibrate)
                    break
            self.ControlBase(CMD.vibrate.value, SWITCH.off.value)
            self.ControlBase(CMD.vibrate.value, SWITCH.off.value)
            self.ControlBase(CMD.vibrate.value, SWITCH.off.value)
            self.ControlBase(CMD.vibrate.value, SWITCH.off.value)
            ret = self.CheckBaseControlResult(CMD.vibrate.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True
        
        #--- main arm lifts up
        self.feedback.substep = 5
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Lift main arm." % (TransEnumStep(self.feedback.step), self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            moveArray = [0, -45, 0, 0]
            ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, moveArray, self.param.velFetchDown)
            if(not ret):
                return False
            else:
                return True


    def StepExecLocaAdjust(self, substep):
        self.feedback.steps = 1
        self.feedback.substeps = 5
        self.feedback.step = STEP.locaAdjust.value

        #--- perform raw scan localization
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Raw scan localization (ignorable)." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            scanType = 3 # right side based positioning
            self.ScanLocalization(CMD.scanLoca.value,
                                  self.param.globalTileLat, 
                                  self.param.globalTileLon,
                                  scanType,
                                  self.param.locaVelMove,
                                  self.param.locaVelScan,
                                  self._scanStartPosLon,
                                  self.param.locaLonEnd,
                                  self._scanStartPosLat,
                                  self.param.locaLatEnd)
            ret = self.CheckBaseControlResult(CMD.scanLoca.value, 100)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True

        #--- perform raw adjustment: use main arm in x & y dir. adjustments
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Raw pose adjustment: half position adj. (ignorable)." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            #--- perform raw angular and positions adjustment
            moveA = self._biasA
            absBiasX = abs(self._biasX)
            absBiasY = abs(self._biasY)
            if(absBiasX > 20):
                moveX = self._biasX / 2.0
            elif(absBiasX < 5):
                moveX = -self._biasX
            else:
                moveX = 0
            if(absBiasY > 20):
                moveY = self._biasY / 2.0
            elif(absBiasY < 5):
                moveY = -self._biasY
            else:
                moveY = 0
            moveY *= 1 if('left' in self.param.locaRefSide) else -1
            print("bias A/X/Y[%.1fdeg,%.1fmm,%.1fmm] arm raw adj A/X/Y[%.1fdeg,%.1fmm,%.1fmm]" 
                  % (self._biasA, self._biasX, self._biasY, moveA, moveX, moveY))
            #--- keep a appropriate gap if too close, to prevent overlay when gap is too small
            ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, moveX, moveY, 0, moveA, 30)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True
            
        #--- perform fine scan localization
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Fine scan localization." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            scanType = 3 # front and right side based scanning for laying tiles
            self.ScanLocalization(CMD.scanLoca.value,
                                  self.param.globalTileLat, 
                                  self.param.globalTileLon,
                                  scanType,
                                  self.param.locaVelMove,
                                  self.param.locaVelScan,
                                  self._scanStartPosLon,
                                  self.param.locaLonEnd,
                                  self._scanStartPosLat,
                                  self.param.locaLatEnd)
            ret = self.CheckBaseControlResult(CMD.scanLoca.value, 100)
            if(not ret):
                return False
            elif(substep == self.feedback.substep):
                return True
        
        #--- perform fine pose adjustment
        self.feedback.substep = 4
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Fine pose adjustment." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            moveA = self._biasA + self.param.layLocaOffsetA
            moveX = self._biasX + self.param.layLocaOffsetX
            moveY = self._biasY
            moveY *= 1 if('left' in self.param.locaRefSide) else -1
            moveY += self.param.layLocaOffsetY
            if(self.WORKEND_TRANS_INSTALLED == 1):
                print(pt.default + "--- Use workend translational mechanism to adjust lay position ---")
                modeInc = False
                self.ControlBase(CMD.workendTrans.value, SWITCH.on.value, modeInc, 5,5, int(moveX), int(moveY))
                ret1 = self.ControlMainArmUntilFinishLinear(MOD.inc.value, 0, 0, 0, moveA, 20)
                ret2 = self.CheckBaseControlResult(CMD.workendTrans.value, 30)
                if((not ret1) or (not ret2)):
                    return False
            else:
                print(pt.default + "--- Use robot arm to adjust lay position ---")
                ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, moveX, moveY, 0, moveA, 20)
                if(not ret):
                    return False
            if(substep == self.feedback.substep):
                return True

        #--- Human intervention
        self.feedback.substep = 5
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Human intervention." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            timeStart = time.time()
            self._confirm   = False
            self._lockHi    = False
            self._flagManip = False
            timeThresh = 0#self.param.timeoutHiNoOp
            while True:
                ret = self.CheckTimeoutAndPreempt(timeStart, timeThresh, "Timeout while control Hi adjustment")
                if(ret == RET.timeout.value):
                    if(timeThresh == self.param.timeoutHiNoOp):
                        rospy.loginfo(pt.green + "[lay][HI] Time due (%ds) while awaiting for human intervention." % timeThresh)
                        retflag = True
                    else:
                        rospy.logerr("[lay][HI] Timeout (%ds) while awaiting for human intervention! Abort." % timeThresh)
                        retflag = False
                    break
                elif(ret == RET.cancel.value):
                    ad.ContinuousMotionStop()
                    ad.ServoPowerOff()
                    retflag = False
                    break
                if(self._flagManip):
                    timeThresh = self.param.timeoutHiOperate
                if(self._confirm):
                    # poseNow = ad.GetCurrentPosition2(1)
                    rospy.loginfo(pt.green + "Comfirmed buttom has been pressed down, quit HI." + pt.default)
                    retflag = True
                    break
            self._lockHi  = True
            self._flagManip = False
            return retflag

    def StepExecLocaAdjust2(self, substep):
        self.feedback.steps = 1
        self.feedback.substeps = 2
        self.feedback.step = STEP.locaAdjust.value

        #--- perform scan localization
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> workend alignment for lay." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
          #  <------------------------------kun_test_begin----------------------------->

            #--- first time (RAW) localization & adjustment: leave a 30 mm gap
            typeLay = 2
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeLay)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            if(not ret):
                ad.ContinuousMotionStop()
                return False
            layside = self.GetLaySide()
            if('left' in layside):
                offsetA = self.param.layLocaOffsetALeft
            elif('middle' in layside):
                offsetA = self.param.layLocaOffsetAMiddle
            elif('right' in layside):
                offsetA = self.param.layLocaOffsetARight
            dx =  self._biasX - 30
            dy = -self._biasY + 30
            da =  self._biasA + offsetA
            dz = 0
            flagPose = self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velLocaAdjLay)
            if(not flagPose):
                    return False

            #--- second time (FINE) localization & adjustment: no more angle adjustment
            typeLay = 2
            self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeLay)
            ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            if(not ret):
                ad.ContinuousMotionStop()
                return False
            layside = self.GetLaySide()
            if('left' in layside):
                offsetX = self.param.layLocaOffsetXLeft
                offsetY = self.param.layLocaOffsetYLeft
            elif('middle' in layside):
                offsetX = self.param.layLocaOffsetXMiddle
                offsetY = self.param.layLocaOffsetYMiddle
            elif('right' in layside):
                offsetX = self.param.layLocaOffsetXRight
                offsetY = self.param.layLocaOffsetYRight
            dx =  self._biasX + self.param.layLocaOffsetX + offsetX
            dy = -self._biasY + self.param.layLocaOffsetY + offsetY
            da =            0 + self.param.layLocaOffsetA
            dz = 0
            flagPose = self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velLocaAdjLay)
            if(not flagPose):
                    return False
            
            #<------------------------------kun_test_end----------------------------->
            
            #<------------------------------kun_test_begin----------------------------->
            # flagCtrlAng = True
            # flagCtrlPosX = True
            # flagCtrlPosY = True
            # prevDy = 10000
            # timeStart = time.time()

            # #添加失败计数器
            # consecutiveFailures = 0
            # #添加允许连续失败最大次数
            # maxConsecutiveFailures = 5

            # # timeDelay = 8
            # while(True):
            #     # time.sleep(timeDelay)
            #     ret = self.CheckTimeoutAndPreempt(timeStart, 60, "lay alignment")
            #     if(ret == RET.timeout.value):
            #         return False
            #     elif(ret == RET.cancel.value):
            #         ad.ContinuousMotionStop()
            #         return False

            #     #--- localization & adjustment
            #     typeLay = 2
            #     self.ControlBase(CMD.scanLoca2.value, SWITCH.on.value, typeLay)
            #     ret = self.CheckBaseControlResult(CMD.scanLoca2.value, 2)
            #     #修改传感数据有效性
            #     if(not ret):
            #         consecutiveFailures += 1
            #         if consecutiveFailures >= maxConsecutiveFailures:
            #             rospy.logwarn("连续%d次传感器数据无效，终止调整" % maxConsecutiveFailures)
            #             ad.ContinuousMotionStop()
            #             return False
            #         rospy.logwarn("传感器数据无效，跳过本次调整")
            #         continue
            #     #重置连续失败次数
            #     consecutiveFailures = 0

            #     #--- check if alignment has been completed
            #     if(flagCtrlAng and abs(self._biasA) < 0.1):
            #         flagCtrlAng = False
            #         print(pt.green+"Ang/PosX/Y[%d%d%d] Angle [%.1f] has been adjusted for fine." % (flagCtrlAng, flagCtrlPosX, flagCtrlPosY, self._biasA))
            #     if(flagCtrlPosX and abs(self._biasX) < 2.0):      #<4.0
            #         flagCtrlPosX = False
            #         print(pt.green+"Ang/PosX/Y[%d%d%d] Position x[%.1f] has been adjusted for fine." % (flagCtrlAng, flagCtrlPosX, flagCtrlPosY, self._biasX))
            #     if(flagCtrlPosY and ((abs(self._biasY) < 2.0) or (self._biasY > prevDy))):   #<6.0
            #         flagCtrlPosY = False
            #         print(pt.green+"Ang/PosX/Y[%d%d%d] Position y[%.1f] has been adjusted for fine." % (flagCtrlAng, flagCtrlPosX, flagCtrlPosY, self._biasY))
            #     if(not flagCtrlAng and not flagCtrlPosX and not flagCtrlPosY):
            #         print(pt.green_bold+"All biases have been elimimated with state: dx[%.1f] dy[%.1f] da[%.1f]"
            #             % (self._biasX, self._biasY, self._biasA))
            #         break

            #     prevDy = self._biasY
            #     dx =  self._biasX / 1.5 if(flagCtrlPosX) else 0
            #     dy = -self._biasY / 1.5 if(flagCtrlPosY) else 0
            #     da =  self._biasA if(flagCtrlAng) else 0
            #     dz = 0
            #     # ad.ControlMainArmLinearInc(dx, dy, dz, da, self.param.velLocaAdjLay, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)
            #     flagPose = self.ControlMainArmUntilFinishLinear(MOD.inc.value, dx, dy, dz, da, self.param.velLocaAdjLay)
            #     if(not flagPose):
            #             return False
                #<------------------------------kun_test_end----------------------------->
         


            if(substep == self.feedback.substep):
                return True

        #--- Human intervention
        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Human intervention." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            timeStart = time.time()
            self._confirm   = False
            self._lockHi    = False
            self._flagManip = False
            timeThresh = 0#self.param.timeoutHiNoOp
            while True:
                ret = self.CheckTimeoutAndPreempt(timeStart, timeThresh, "Timeout while control Hi adjustment")
                if(ret == RET.timeout.value):
                    if(timeThresh == self.param.timeoutHiNoOp):
                        rospy.loginfo(pt.green + "[lay][HI] Time due (%ds) while awaiting for human intervention." % timeThresh)
                        retflag = True
                    else:
                        rospy.logerr("[lay][HI] Timeout (%ds) while awaiting for human intervention! Abort." % timeThresh)
                        retflag = False
                    break
                elif(ret == RET.cancel.value):
                    ad.ContinuousMotionStop()
                    ad.ServoPowerOff()
                    retflag = False
                    break
                if(self._flagManip):
                    timeThresh = self.param.timeoutHiOperate
                if(self._confirm):
                    # poseNow = ad.GetCurrentPosition2(1)
                    rospy.loginfo(pt.green + "Comfirmed buttom has been pressed down, quit HI." + pt.default)
                    retflag = True
                    break
            self._lockHi  = True
            self._flagManip = False
            return retflag

    def StepExecLocaFine(self, substep, angleOrPos):
        self.feedback.steps = 1
        self.feedback.substeps = 4
        self.feedback.step = STEP.moveAdjFineAngle.value if('angle' in angleOrPos) else STEP.moveAdjFinePos.value

        #--- turn on the lamp
        self.feedback.substep = 1
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn on lamp." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.lamp.value, SWITCH.on.value)
            ret = self.CheckBaseControlResult(CMD.lamp.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False
            if(substep == self.feedback.substep):
                return True

        self.feedback.substep = 2
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Send CV positioning request." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            funcRetL = False
            funcRetR = False
            result, biasX, biasY, biasA = self.RequestImageProcess('left-ld2')
            if(result == 'succeeded'):
                self._result = result
                biasXLeft = biasX
                biasYLeft = biasY
                biasALeft = biasA
                funcRetL = True
            else:
                self._result = result
                self._biasX = 0
                self._biasY = 0
                self._biasA = 0
                rospy.logwarn("[LocaFine] Failed to localization via left camera [%s]" % result)
                funcRetL = False
            result, biasX, biasY, biasA = self.RequestImageProcess('right-ld2')
            if(result == 'succeeded'):
                self._result = result
                biasXRight = biasX
                biasYRight = biasY
                biasARight = biasA
                funcRetR = True
            elif(result == 'failed'):
                self._result = result
                self._biasX = 0
                self._biasY = 0
                self._biasA = 0
                rospy.logwarn("[LocaFine] Failed to localization via right camera [%s]" % result)
                funcRetR = False

            if((funcRetL == False) or (funcRetR == False)):
                return False

            self._biasX = (biasXLeft - biasXRight) / 2  * self.param.locaFineScalarX
            self._biasY = (biasYLeft - biasYRight) / 2  * self.param.locaFineScalarY
            self._biasA = math.atan2((biasXLeft + biasXRight) * self.param.locaFineScalarX, self.param.locaFineDistCam) * self.param.locaFineScalarA
            print(angleOrPos + "------------------------------------------------------")
            print("[LocaFine]  Left: A:%.3f deg, X:%.3f pixels, Y:%.3f pixels" % (biasALeft, biasXLeft, biasYLeft))
            print("[LocaFine] Right: A:%.3f deg, X:%.3f pixels, Y:%.3f pixels" % (biasARight, biasXRight, biasYRight))
            print("[LocaFine]   SYN: A:%.5f deg, X:%.1f mm,     Y:%1f mm" % (self._biasA*TASK.RAD2DEG,self._biasX,self._biasY))
            print("----------------------------------------------------------------")

            if(substep == self.feedback.substep):
                return True

        #--- turn off the lamp
        self.feedback.substep = 3
        if((substep == 0) or (substep == self.feedback.substep)):
            rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Turn off lamp." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            self.ControlBase(CMD.lamp.value, SWITCH.off.value)
            ret = self.CheckBaseControlResult(CMD.lamp.value, self.param.timeoutControlBaseReply)
            if(not ret):
                return False 
            if(substep == self.feedback.substep):
                return True
        
        #--- adjust the pose of the workend according to results of the image-positioning [TODO:PSD pose bias compensation]
        self.feedback.substep = 4
        if((substep == 0) or (substep == self.feedback.substep)):
            layside = self.GetLaySide()
            if('ang' in angleOrPos):    # 'angle'/'angular'
                # printStr = "Adjust lay ANGLE via CV."
                if('left' in layside):
                    offsetA = self.param.adjFineOffsetLA
                elif('middle' in layside):
                    offsetA = self.param.adjFineOffsetCA
                elif('right' in layside):
                    offsetA = self.param.adjFineOffsetRA
                moveX = 0
                moveY = 0
                moveA = self._biasA * TASK.RAD2DEG + self.param.adjFineOffsetA + offsetA
                print("offsetA [%.1f] by side [%s]" % (offsetA, layside))
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Adjust lay ANGLE via CV." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            elif('pos' in angleOrPos):
                # printStr = "Adjust lay POSITION via CV."
                if('left' in layside):
                    offsetX = self.param.adjFineOffsetLX
                    offsetY = self.param.adjFineOffsetLY
                elif('middle' in layside):
                    offsetX = self.param.adjFineOffsetCX
                    offsetY = self.param.adjFineOffsetCY
                elif('right' in layside):
                    offsetX = self.param.adjFineOffsetRX
                    offsetY = self.param.adjFineOffsetRY
                moveX = self._biasX + self.param.adjFineOffsetX + offsetX
                moveY = self._biasY + self.param.adjFineOffsetY + offsetY
                moveA = 0
                print("offsetX [%.1f] offsetY [%.1f] by side [%s]", offsetX, offsetY, layside)
                rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Adjust lay POSITION via CV." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
            self.actionServer.publish_feedback(self.feedback)
            rospy.logwarn("[%s]: adjX[%.3f]mm adjY[%.3f]mm adjA[%.3f]deg" % (angleOrPos, moveX, moveY, moveA))
            if('ang' in angleOrPos):
                # posArray = self.MainArmKinematics(moveX, moveY, moveA)
                posArray = [0, 0, 0, moveA]
                ret = self.ControlMainArmUntilFinishJoint(MOD.inc.value, posArray, 1)
            elif('pos' in angleOrPos):
                # poseJoint = ad.GetCurrentPosition(0)
                # moveX2, moveY2 = self.AngleBiasBasedLinearMotionControl(poseJoint[0], poseJoint[3], moveX, moveY)
                moveX2 = moveX
                moveY2 = moveY
                if(self.WORKEND_TRANS_INSTALLED == 1):
                    print("--- Use workend translational mechanism to adjust lay position ---")
                    self.ControlBase(CMD.workendTrans.value, SWITCH.on.value, 5,5, moveX2, moveY2)
                    ret = self.CheckBaseControlResult(CMD.workendTrans.value, 30)
                else:
                    print("--- Use robot arm to adjust lay position ---")
                    ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, moveX2, moveY2, 0, moveA, 50)
            if(not ret):
                return False
            else:
                return True


    def StepExecAdjHuman(self):
        '''
        This function execute main arm adjustment in terms of angle and position, by Human worker
        '''
        self.feedback.steps = 1
        self.feedback.substeps = 1
        self.feedback.step = STEP.moveAdjHuman.value
        
        #--- check whether human intervention is needed
        self.feedback.substep = 1
        rospy.loginfo(pt.cyan_bold+"[%s]<%d/%d> Adjust lay pose via HI." % (TransEnumStep(self.feedback.step) ,self.feedback.substep, self.feedback.substeps))
        self.actionServer.publish_feedback(self.feedback)
        timeStart = time.time()
        self._confirm   = False
        self._lockHi    = False
        self._flagManip = False
        timeThresh = 5                # increase wait time if human intervention is triggerred
        # poseBegin = ad.GetCurrentPosition2(1)
        while True:
            ret = self.CheckTimeoutAndPreempt(timeStart, timeThresh, "Timeout while control Hi adjustment")
            if(ret == RET.timeout.value):
                if(timeThresh == 5):
                    rospy.loginfo(pt.green + "[lay][HI] Time due (5s) while awaiting for human intervention."+pt.default)
                    retflag = True
                else:
                    rospy.logerr("[lay][HI] Timeout (3min) while awaiting for human intervention! Abort.")
                    retflag = False
                break
            elif(ret == RET.cancel.value):
                ad.ContinuousMotionStop()
                ad.ServoPowerOff()
                retflag = False
                break
            if(self._flagManip):
                timeThresh = 180
            if(self._confirm):
                rospy.loginfo(pt.green + "Comfirmed buttom has been pressed down, quit HI." + pt.default)
                retflag = True
                break
        self._lockHi  = True
        self._flagManip = False
        return retflag


    def StepExecOneLoop(self, laySide, daubheight):
        '''
        This function execute a serial of steps to lay middle/left/right side tiles.
        Arg LAYSIDE can be 'middle'/'left'/'right'
        '''
        if('middle' in laySide):
            macroStepName = STEP.macroMiddle.value
            stepName = STEP.moveMiddle.value
        elif('left' in laySide):
            macroStepName = STEP.macroLeft.value
            stepName = STEP.moveLeft.value
        elif('right' in laySide):
            macroStepName = STEP.macroRight.value
            stepName = STEP.moveRight.value

        self.feedback.macrostep = macroStepName
        self.feedback.macrosubsteps = 9

        self.feedback.macrosubstep = 1
        if(not self.StepExecFetch(0)): # 0: execute all substeps in this step
            return False

        self.feedback.macrosubstep = 2
        if(not self.StepExecMoveFront2(laySide, daubheight)):
            return False

        self.feedback.macrosubstep = 3
        if(not self.StepExecDescend(stepName, 0)):
            return False

        self.feedback.macrosubstep = 4
        if(not self.StepExecLocaFine(0, 'angle')):
            return False

        self.feedback.macrosubstep = 5
        if(not self.StepExecLocaFine(0, 'pos')):
            return False

        self.feedback.macrosubstep = 6
        if(not self.StepExecAdjHuman()):
            return False

        self.feedback.macrosubstep = 7
        if(not self.StepExecLay(0)):
            return False

        self.feedback.macrosubstep = 8
        if(not self.StepExecMoveRear2()):
            return False

        self.feedback.macrosubstep = 9
        if(not self.StepExecStepback()):
            return False
        else:
            return True


    def StepExecAll(self, height):
        self.feedback.macrostep = STEP.macroAll.value
        self.feedback.macrosubsteps = 3

        self.feedback.macrosubstep = 1
        if(not self.StepExecOneLoop('middle', height)):
            return False

        self.feedback.macrosubstep = 2
        if(not self.StepExecOneLoop('left', height)):
            return False

        self.feedback.macrosubstep = 3
        if(not self.StepExecOneLoop('right', height)):
            return False
        else:
            return True



#========================================= AUXILLIARY FUNCTIONS =========================================

    def AcqPosArm(self):
        poseCoord = ad.GetCurrentPosition2(1)
        time.sleep(0.1)
        poseJoints = ad.GetCurrentPosition(0)
        time.sleep(0.1)
        rospy.loginfo("[ACQ] current main arm's positions are as follows:")
        rospy.loginfo("[Cartesian] X:%.2fmm, Y:%.2fmm, Z:%.2fmm, U:%.2fdeg" % (poseCoord[0], poseCoord[1], poseCoord[2], poseCoord[3]*57.29578))
        rospy.loginfo("[Joints] J1:%.2fdeg, J2:%.2fmm, J3:%.2fmm, J4:%.2fdeg" % (poseJoints[0], poseJoints[1], poseJoints[2], poseJoints[3]))
        return True

#========================================= TEST FUNCTIONS =========================================

    def TestMainArmLinearAbs(self, moveY):
        movex = 1338
        movez = -155
        movea = 0
        vel = 100
        rospy.loginfo("[TEST] move main arm to xyza[%dmm,%dmm,%dmm,%ddeg] vel[%d]", movex, moveY, movez, movea, vel)
        ret = self.ControlMainArmUntilFinishLinear(MOD.abs.value, movex, moveY, movez, movea, vel)
        if(not ret):
            return False
        else:
            return True

    def TestMainArmLinearInc(self):
        movex = -100
        movey = 100
        movez = 0
        movea = 0
        vel = 10
        rospy.loginfo("[TEST] move main arm to xyza[%dmm,%dmm,%dmm,%ddeg] vel[%d]", movex, movey, movez, movea, vel)
        ret = self.ControlMainArmUntilFinishLinear(MOD.inc.value, movex, movey, movez, movea, vel)
        if(not ret):
            return False
        else:
            return True

    def TestMyKineLinearCtrl(self):
        posArray = self.MainArmKinematics(-100, 100, 0.0)
        ret = self.ControlMainArmUntilFinishJoint(MOD.abs.value, posArray, 10)
        if(not ret):
            return False
        else:
            return True

    def TestContinuousManip(self, type):
        if(type == 0):
            print("[0] Test contiunous motion: forward.")
            arrayPos = []
            arrayCoord = [0, 0, 0, 1, 0]
            arrayPos.append(self.param.armPosDaubT1)
            arrayPos.append(self.param.armPosDaubT2)
            arrayPos.append(self.param.armPosDaubS)
            arrayPos.append(self.param.armPosLeftG)
            arrayPos.append(self.param.armPosLeftT)
            arrayVel = []
            arrayVel.append(self.param.armVelDaubT1)
            arrayVel.append(self.param.armVelDaubT2)
            arrayVel.append(self.param.armVelDaubS)
            arrayVel.append(self.param.armVelLeftG)
            arrayVel.append(self.param.armVelLeftT)
            ad.ContinuousMotionQueue(1, arrayCoord, arrayPos, arrayVel, 5, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)
        elif(type == 10):
            print("[0] Test contiunous motion: all line waypoints.")
            arrayPos = []
            arrayCoord = [0, 1, 1]
            # arrayPos.append(self.param.waypointInitPos)
            arrayPos.append(self.param.armPosDaubT1)
            arrayPos.append(self.param.waypointBackAvo)
            arrayPos.append(self.param.waypointDaubStart)
            arrayVel = []
            arrayVel.append(self.param.armVelDaubT1)
            arrayVel.append(self.param.velocityBackAvo)
            arrayVel.append(self.param.velocityDaubStart)
            ad.ContinuousMotionQueue(1, arrayCoord, arrayPos, arrayVel, 3, self.param.velArmGlobalPercent, self.param.velArmGlobalAcc)
        elif(type == 1):
            print("[1] Test contiunous motion: stop")
            ad.ContinuousMotionStop()
        elif(type == 2):
            print("[2] Test contiunous motion: suspend")
            ad.ContinuousMotionSuspend()
        elif(type == 3):
            print("[3] Test contiunous motion: start")
            ad.ContinuousMotionStart()
        return True


#=======================================================================================================
#==============================  MAIN FUNCTION OF PROGRAM HEREAFTER  ===================================
#=======================================================================================================

if __name__ == '__main__':

    #--- load all parameters of workflow
    rospy.loginfo(pt.bold+"[main] loading parameters of workflow from yaml file(s) ..."+pt.default)
    paramWorkFlow = PARAM(dir + "/cfg/tiling.yaml")


    # 机械臂控制已迁移到ZMotion控制器，通过control_base节点控制
    print("Main arm control migrated to ZMotion controller via control_base node.")
    print("No need to connect to iNexBot controller anymore.")

    rospy.init_node('main_logic', anonymous=True)

    print("main_logic node is running...")
    print(ad.GetServoStatus())
    #--- Define in sequence: srvClient, msgPubBaseCmd, msgSubBaseStatus, msgSubWorkend
    task = TASK(param = paramWorkFlow, \
                srvNameCV        = 'srv_image_process',     \
                srvNameHI        = 'human_intervention',    \
                msgPubBaseCmd    = 'base_cmd',              \
                msgPubBaseErr    = 'base_error',            \
                msgSubBaseStatus = 'base_response',         \
                msgSubHi         = '/hi_adjust',            \
                msgSubSensors    = '/peripheral_sensor'     )

    # 机械臂速度控制已迁移到control_base，通过ROS参数设置
    print(pt.yellow+"Main arm speed control migrated to control_base node: {}%".format(task.param.velArmGlobalPercent))

    # The final addition, rospy.spin() simply keeps your node from exiting
    # until the node has been shutdown. Unlike roscpp, rospy.spin() does not
    # affect the subscriber callback functions, as those have their own threads.
    rospy.spin()


    rospy.loginfo(pt.bold+"[main] main logic node terminated. Main arm control migrated to ZMotion."+pt.default)

