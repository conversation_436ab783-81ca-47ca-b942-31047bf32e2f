{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "include", "generate_messages"], "files": ["/home/<USER>/catkin_ws/build/blr/main_logic/cmake/main_logic-genmsg.cmake", "/opt/ros/noetic/share/genmsg/cmake/genmsg-extras.cmake", "blr/main_logic/CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 2, "file": 2, "line": 78, "parent": 0}, {"command": 1, "file": 1, "line": 307, "parent": 1}, {"file": 0, "parent": 2}, {"command": 0, "file": 0, "line": 36, "parent": 3}]}, "id": "_main_logic_generate_messages_check_deps_taskActionFeedback::@93202e25429a7a15bd99", "name": "_main_logic_generate_messages_check_deps_taskActionFeedback", "paths": {"build": "blr/main_logic", "source": "blr/main_logic"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "/home/<USER>/catkin_ws/build/blr/main_logic/CMakeFiles/_main_logic_generate_messages_check_deps_taskActionFeedback", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/catkin_ws/build/blr/main_logic/CMakeFiles/_main_logic_generate_messages_check_deps_taskActionFeedback.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}