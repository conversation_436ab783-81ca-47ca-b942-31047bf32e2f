{"backtrace": 9, "backtraceGraph": {"commands": ["add_custom_target", "find_package"], "files": ["/opt/ros/noetic/share/std_msgs/cmake/std_msgsConfig.cmake", "/opt/ros/noetic/share/rosgraph_msgs/cmake/rosgraph_msgsConfig.cmake", "/opt/ros/noetic/share/roscpp/cmake/roscppConfig.cmake", "/opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake", "blr_param/CMakeLists.txt"], "nodes": [{"file": 4}, {"command": 1, "file": 4, "line": 11, "parent": 0}, {"file": 3, "parent": 1}, {"command": 1, "file": 3, "line": 76, "parent": 2}, {"file": 2, "parent": 3}, {"command": 1, "file": 2, "line": 197, "parent": 4}, {"file": 1, "parent": 5}, {"command": 1, "file": 1, "line": 197, "parent": 6}, {"file": 0, "parent": 7}, {"command": 0, "file": 0, "line": 184, "parent": 8}]}, "id": "std_msgs_generate_messages_nodejs::@703c88a6609e6f0ac1ec", "name": "std_msgs_generate_messages_nodejs", "paths": {"build": "blr_param", "source": "blr_param"}, "sources": [], "type": "UTILITY"}