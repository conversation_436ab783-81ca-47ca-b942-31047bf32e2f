/*------------------------------------------------------------------------------------------------------------
AUTHOR: YUN LING
DATE: Jan 30, 2022
DESCRIBE: 	1. Subscribe sensor msgs. such as IMU and PSD, from node "imu_base", "imu_workend" and "psd_node";
			2. Subscribe task command msgs. from node "main_logic";
			3. Subscribe xbox joystick msgs. from node "joy_node"
			4. Publish response msg. to tell various status;
			5. Execute control functions;
			6. Control objects include: move base, paver, workend, shotcreting modules
    TODO:
			#1. Integrate support control and related remote control in;
			#2. Add soft limit by calling function;
			#3. Add kinematics model and tacking control algorithm;
			#4. Modify IMU and PSD nodes, as well as msg types;
			#5. Add paver leveling control and basic controls;
			x6. Add workend leveling control and basic controls;
			#7. Improve base leveling control;
			#8. Add description of commands functions for printing;
			#9. Update documentations of command msgs;
			10. Add init. check of encoder readings, alarm if abnormal;
		   #11. Add regular check of support distance, forbid steering when shrinking;
		   #12. Modify Sppt(); (PS: Instead achieves by macro definitions)
			13. Add functions of setting zero position for YZ motors
		   #14. Add CAN reception and parsing threading
		   x15. Reply busy if last task is not finished
		   #16. In-situ spin move control via remote controller
		   #17. Update command's params. for immediate execution if the same task is running
		   #18. Flush uart buffer when going to receive new data from uart port
		   #19. Update yaml files by command, not re-launch the node
		   #20. Add in new kinematic model of robot platform
			21. Complete all tasks which need reply succeeded by judging encoder positions
		   x22. Try multi-axises movement control in "joint steering"
		   #23. Add in new shotcreting module control with teach tech.
		   #24. Add position acquisition of CAN bus motors
		   x25. Use CONNFRAME function of ZMOTION to control shotcreting
		   #26. Use RECONNFRAME function of ZMOTION to control shotcreting
		   #27. Add workend leveling control by using axis 12 and 13, change paver deliver axis to 14
		   #28. Add calculation of base's orientation by using front and rear PSD values
		    29. Add control functions of RuiTe drivers in 'can_drive'.
		G3.2
		   #30. Remove CAN drivers and interface functions
			31. Modify paver lift/roll motors control from CAN bus based to EtherCat based
		BLR#3 (1st machine in Jurong railway station)
			32. Update enumerator of CMD_Types
			33. Adjust roll angle to a goal angle (may not horizontal) for workend leveling
			34. Improve the paver pose control that laser line stays on different goal heights of left and right PSDs
			35. Add pitch bias parameter in workend leveling, as well as check no-changing imu data in pitch & roll
		BLR#4
		   #36. Add laser based distance measurement module, and integrate it into CMD_trackLine
		   #37. Add two more axis motors for tiles alignment control, which executes when receives CMD_tileAlign
		TODO:
		    38. Cancel illegal values check during yaml file loading, just print those zero value params. on screen
			39. Re-organize all zmotion parameters in yaml file and load them in a struct group
			40. Ajust all params for zmotion initialization in the same style to zAlign
		[20230605/0619]
		    41. Add function of clearing all alarms for Zmotion controller
			42. [0624] Add reversed tracking function, by recognizing the negative speed
		[20230715/0716]
		    43. Adjust the enumerator of CMD_Type with segmental index
			44. Add function of CMD_workendAlign to adjust pose to make workend parallel to tiles
			45. Optimize the Zmotion axis configuration parameters of modue: workend, paver, in a standard way
		[20230925]
		    46. Use MCU-managed sensor data reception & peripheral control, hence remove all UART libs;
			47. Re-construct the Zmotion lib in a more professional way;
			48. Remove the param_loder.*pp files for clean and tide construction.
		[20231030]
		    49. Add remote control safety mechanism through xbox data reception time check;
			50. Add new command function of measuring angle and distance bias of tiles before laying.
		[20231103]
		    46. Optimize the remote control by automatically check xbox message, make the control when data are received.
			47. Create new parameters for motion_control.cpp.
		[20240108][BIG CHANGE]
		    48. Integrate recsensor.cpp in as a thread to fasten sensor-data-reception process.
		[20240129][USE GIT TO MANAGE VERSIONS FROM NOW ON]
-----------------------------------------------------------------------------------------------------------*/
#include "ros/ros.h"
#include "std_msgs/Int32MultiArray.h"   // for subscribing commands
#include "std_msgs/Int32.h"   			// for publishing scanner commands
#include "std_msgs/String.h"            // for publishing response/error msgs
#include "scan_camera/profiles.h"      // for subscribing scanner data
// #include "sensor_msgs/Imu.h"
#include "sensor_msgs/Joy.h"
#include "control_base/sensor_data.h"

#include "blr_common.hpp"
#include "motioncontrol.hpp"
#include "lingzmotion.hpp"
#include "recsensor.hpp"

//#define SENSOR_REC_ALARM  // print warnings if sensor reception is abnormal

#define EXEC_OFF	0		// task execution switcher: turn off
#define EXEC_ON		1		// task execution switcher: turn on
#define SET_INIT	0		// for task execution which needs init. settings
#define SET_LOOP	1		// for task execution which needs loop control
#define SET_STEP1	2		// for those need multiple-steps tasks
#define SET_STEP2	3
#define SET_STEP3	4
#define SET_STEP4	5
#define SET_STEP5	6

using namespace std;


//--- Global struct or class instance list
// MOTION_PARAM   	mp; 	// control value scalars included
MOTION_CONTROL 	mc;     	// control functions included
LINGZMOTION     lzm;
XBOX 			xbox;
SENSOR_REC      rs;
SCANNER         scanner;

//--- Global variables list
int flagExec[200];            	// execute some task when true
int flagExecSteps[200];     	// for tasks awaiting finish after once execute
int cmdArray[200][FRAME_LEN];
int axisPosArray[32];         	// for error checking, by recording the positions before motion control
int nodeRest, nodeAll;			// for frame5 control of main arm using zMotion controller

double timeRecXbox = 0;
bool flagRecScanner = false;

ros::Publisher pubReply;
ros::Publisher pubError;
ros::Publisher pubScannerCmd;
// ros::Publisher pubSensor;
std_msgs::Int32MultiArray msgReply;
std_msgs::String msgError;
std_msgs::Int32 msgScannercmd;

//--- Functions that defined in this file
void PublishResponse(unsigned int taskName, unsigned char state);
void PublishResponse(unsigned int taskName, unsigned char state, int index);
void PublishResponse(unsigned int taskName, unsigned char state, float da, float dx, float dy, float len, float width, float toEdgeLon, float toEdgeLat);
void PublishResponse(unsigned int taskName, unsigned char state, float da, float dx, float dy, float len, float width, float pitch, float roll, float dz, float heightFL, float heightFR, float heightR, float heightL,float heightRC);
void PublishError(int error);
void PublishError(int error, string description);
void PublishScannerCmd(int cmd);
void TaskExecFunc(int cmdArray[][FRAME_LEN], double timeCurrent);

//--- Callback functions
// void CallbackSensor(const control_base::sensor_data::ConstPtr &msg); // [20230918]
void CallbackCmd(const std_msgs::Int32MultiArray::ConstPtr &msg);
void CallbackScannerData(const scan_camera::profiles::ConstPtr &msg);
void CallbackXbox(const sensor_msgs::Joy::ConstPtr &msg);
void CallbackTileSize(const std_msgs::Int32MultiArray::ConstPtr &msg);


//================================ MAIN FUNCTION HEREAFTER =============================

int main(int argc, char **argv)
{
	ros::init(argc, argv, "command_process");
	ros::NodeHandle nh("~");    // add ("~") will load node-private parameters in launch file
	ros::Subscriber subCmd      = nh.subscribe("/base_cmd",    	2, CallbackCmd        );
	ros::Subscriber subXbox     = nh.subscribe("/joy",        	1, CallbackXbox       );
	ros::Subscriber subScanner  = nh.subscribe("/profile_data", 1, CallbackScannerData);
	ros::Subscriber subTileSize = nh.subscribe("/tilesize",  	1, CallbackTileSize   );
	pubReply = nh.advertise<std_msgs::Int32MultiArray>("/base_response", 3);
	pubError = nh.advertise<std_msgs::String>("/base_error", 3);
	pubScannerCmd = nh.advertise<std_msgs::Int32>("/query_profile", 1);
	//pubSensor = nh.advertise<control_base::sensor_data>("/peripheral_sensor", 3);
	ros::Rate r(90);

	// ros::Duration delay(5.0); // delay 5s
	// ros::Duration(delay).sleep();
	// PublishError(12);
	// PublishError(17);
	// PublishError(17, "let's dance!");

	//--- load directory from launch file
	string pathPack;
	nh.param<string>("pathCfgFile", pathPack, "/home/<USER>/catkin_ws/src/blr/control_base");
	ROS_INFO("Find current path of this package: '%s'", pathPack.c_str());
	
	std::string ipAddr;
    int portRecfrom;
    int portSendto;
    // int exceptionThresh;
    if(!nh.getParam("ip_addr", ipAddr))
    {
        ipAddr = "*************";
        ROS_ERROR("[IMU] Failed to load parameter 'ip_addr'! Use the default value [%s]", ipAddr.c_str());
    }
    if(!nh.getParam("port_recfrom", portRecfrom))
    {
        portRecfrom = 10001;
        ROS_ERROR("[IMU] Failed to load parameter 'port_recfrom'! Use the default value [%d]", portRecfrom);
    }
    if(!nh.getParam("port_sendto", portSendto))
    {
        portSendto = 10002;
        ROS_ERROR("[IMU] Failed to load parameter 'baudrate'! Use the default value [%d]", portSendto);
    }

	//--- initialize ZMOTION axises
	int retLZM = lzm.Initialization(pathPack+"/cfg/lingzmotion.yaml");
	if(retLZM)
		return 1;
	mc.Initialization(pathPack+"/cfg/controlparam.yaml");

	vector<int> axisList = lzm.GetAxisOutLimit();
	for(int idx=0; idx<axisList.size(); idx++)
		PublishError(ERR_axisOutLimit, lzm.TransEnumToStringAxis(axisList[idx]));
	
	//--- initilize receive sensor data class
	rs.LoadScannerRange(mc.GetScanRangeMin(), mc.GetScanRangeMax(), mc.GetScanRangeOut());
	rs.Initialization(ipAddr, portRecfrom, portSendto);

	//--- clear all task execution steps
	memset(flagExecSteps, SET_INIT, sizeof(flagExecSteps));

	ROS_INFO(WHITE_BOLD "Node 'control_base_node' is ready and running..." ENDL);
	long cnt = 0, cnt2 = 0;
	double timeLastRecord = 0;
	double timeLastRecord2 = 0;
	while(ros::ok())
	{
		TaskExecFunc(cmdArray, ros::Time::now().toSec());

#ifdef SENSOR_REC_ALARM
		if(rs.timeLastExec_ - timeLastRecord < 1)
		{
			if(cnt++ > 100)
				ROS_ERROR("Sensor parser thread abnormal with last moment: [%.1f]ms counts: [%ld]", rs.timeLastExec_, cnt++);
		}
		else
		{
			cnt = 0;
			timeLastRecord = rs.timeLastExec_;
		}
		if(rs.timeLastRec_ - timeLastRecord2 < 1)
		{
			if(cnt2++ > 100)
				ROS_WARN("Sensor Rec. thread abnormal with last moment: [%.1f]ms counts: [%ld]", rs.timeLastRec_, cnt2++);
		}
		else
		{
			cnt2 = 0;
			timeLastRecord2 = rs.timeLastRec_;
		}
#endif

		ros::spinOnce();
		r.sleep();
	}

	ROS_INFO("Node 'command_process_node' terminated.");    // this line cannot print
	return 0;
}

//============================== CALLBACK functions hereafter ==============================

void CallbackCmd(const std_msgs::Int32MultiArray::ConstPtr &msg)
{
	unsigned int taskName = msg->data[0];
	string op;
	op = msg->data[1]==1 ? "ON":"OFF";
	op = msg->data[1]==2 ? "VACUMM_RELEASE":op;
	if(taskName < CMD_end)
		ROS_INFO("Received Command: [%d][%s][%s]", taskName, op.c_str(), TransCmdEnum(taskName).c_str());

	unsigned int replyName = msg->data[1]==1 ? REPLY_received:REPLY_terminated;
	PublishResponse(taskName, replyName);

	if(taskName == CMD_null)
	{
		memset(flagExec, EXEC_OFF, sizeof(flagExec));  // clear all task flags
	}
	else if((taskName > CMD_null) && (taskName < CMD_end))
	{
		if((flagExec[taskName] == EXEC_ON) && (msg->data[1] == 1))
		{
			ROS_WARN("Task [%d][%s] is running currently.", taskName, TransCmdEnum(taskName).c_str());
			ROS_WARN("Will update new parameters of this task, and execute it instantly.");
			flagExecSteps[taskName] = SET_INIT;
			memcpy(cmdArray[taskName], &msg->data[1], FRAME_LEN * sizeof(int));
			PublishResponse(taskName, REPLY_preempted);
			//ROS_WARN("Wait until current task's completion, or terminate it before sending the same one.");
			//PublishResponse(taskName, REPLY_busy);
		}
		else
		{
			flagExec[taskName] = EXEC_ON;
			memcpy(cmdArray[taskName], &msg->data[1], FRAME_LEN * sizeof(int));
		}
	}
	else
	{
		// ROS_WARN("No matching command found with reception number: [%d]", taskName);
	}
}

void CallbackScannerData(const scan_camera::profiles::ConstPtr &msg)
{
	scanner.frontleft  = msg->frontLeft;
	scanner.frontright = msg->frontRight;
	scanner.rearcenter = msg->rear;           
	scanner.left       = msg->left;
	scanner.right      = msg->right;
	flagRecScanner = true;
}

void CallbackTileSize(const std_msgs::Int32MultiArray::ConstPtr &msg)
{
	float tileLon = msg->data[0];
	float tileLat = msg->data[1];
	mc.SetTileSize(tileLon, tileLat);
	ROS_INFO("Got the tile size: lon [%.0f] lat [%.0f].", tileLon, tileLat);
}

void PublishResponse(unsigned int taskName, unsigned char state)
{
	msgReply.data.clear();
	msgReply.data.push_back(taskName);
	msgReply.data.push_back(state);
	pubReply.publish(msgReply);
}
void PublishResponse(unsigned int taskName, unsigned char state, int index)
{
	msgReply.data.clear();
	msgReply.data.push_back(taskName);
	msgReply.data.push_back(state);
	msgReply.data.push_back(index);
	pubReply.publish(msgReply);
}

void PublishResponse(unsigned int taskName, unsigned char state, float da, float dx, float dy, float len, float width, float pitch, float roll, float dz, float heightFL, float heightFR, float heightR, float heightL,float heightRC)
{
	msgReply.data.clear();
	msgReply.data.push_back(taskName);
	msgReply.data.push_back(state);

	msgReply.data.push_back(da);
	msgReply.data.push_back(dx);
	msgReply.data.push_back(dy);

	msgReply.data.push_back(len);
	msgReply.data.push_back(width);

	msgReply.data.push_back(pitch);
	msgReply.data.push_back(roll);
	msgReply.data.push_back(dz);

	msgReply.data.push_back(heightFL);
	msgReply.data.push_back(heightFR);
	msgReply.data.push_back(heightR);
	msgReply.data.push_back(heightL);
	msgReply.data.push_back(heightRC);
	pubReply.publish(msgReply);
}
void PublishError(int error)
{
	char str[100];
	sprintf(str, "%d:", error);
	msgError.data = std::string(str);
	pubError.publish(msgError);
}
void PublishError(int error, string description)
{
	char str[100];
	sprintf(str, "%d: %s", error, description.c_str());
	msgError.data = std::string(str);
	pubError.publish(msgError);
}
void PublishScannerCmd(int cmd)
{
	flagRecScanner = false;
	msgScannercmd.data = cmd;
	pubScannerCmd.publish(msgScannercmd);
}

//--- Just compute control pos. & vel. values of related axis
void TaskExecFunc(int cmdArray[][FRAME_LEN], double timeCurrent)
{
	//-------------------- measure angular/position bias of tiles -------------------
	if(flagExec[CMD_scanLoca])
	{
		if(cmdArray[CMD_scanLoca][0])
		{
			static double timeBegin = 0;
			static double timeSpanInit, timeSpanScan;
			static vector<pair<float,float>> heightsFL, heightsFR, heightsRC, heightsL, heightsR;
			static unsigned char locaType = 1; // 1: lay:front scan; 2:lay:front & side scan; 3-fetch:front & side scan
			static float startPointLon, endPointLon;
			static float startPointLat, endPointLat;
			static float speedScan, speedMove;
			static bool flagArrivalLon, flagArrivalLat;

			if(flagExecSteps[CMD_scanLoca] == SET_INIT)
			{
				float tileLength = cmdArray[CMD_scanLoca][1];
				float tileWidth  = cmdArray[CMD_scanLoca][2];
				mc.LoadTileSize(tileLength, tileWidth);
				locaType      = cmdArray[CMD_scanLoca][3];
				speedMove     = cmdArray[CMD_scanLoca][4];
				speedScan     = cmdArray[CMD_scanLoca][5];
				startPointLon = cmdArray[CMD_scanLoca][6];
				endPointLon   = cmdArray[CMD_scanLoca][7];
				startPointLat = cmdArray[CMD_scanLoca][8];
				endPointLat   = cmdArray[CMD_scanLoca][9];
				if     (locaType == LOCA_layByAngle)	ROS_INFO("\n=== Got localization task: lay(front scan)===");
				else if(locaType == LOCA_layByLeft  )	ROS_INFO("\n=== Got localization task: lay(front & left scan)===");
				else if(locaType == LOCA_layByRight )	ROS_INFO("\n=== Got localization task: lay(front & right scan)===");
				else if(locaType == LOCA_fetch      )	ROS_INFO("\n=== Got localization task: fetch(front & side scan)===");
				else									ROS_ERROR("\n=== Got localization task: incorrect loca. type [%d] ===", locaType);

				//--- clear all data
				heightsFL.clear();
				heightsFR.clear();
				heightsL.clear();
				heightsR.clear();

				//--- compute time span for init. & scan steps
				float posNowLon = lzm.GetAxisPosition(AXIS_scannerLon);
				float posNowLat = lzm.GetAxisPosition(AXIS_scannerLat);
				float timeSpanInitLon = fabs(posNowLon   - startPointLon) / speedMove +  5.0;
				float timeSpanInitLat = fabs(posNowLat   - startPointLat) / speedMove +  5.0;
				float timeSpanScanLon = fabs(endPointLon - startPointLon) / speedScan + 10.0;
				float timeSpanScanLat = fabs(endPointLat - startPointLat) / speedScan + 10.0;
				timeSpanInit = max(timeSpanInitLon, timeSpanInitLat);
				timeSpanScan = max(timeSpanScanLon, timeSpanScanLat);
				// printf("posNow: %.1f, speedInit:%.1f, span: %.1f\n", posNow, speedInit, timeSpanInit);
				// printf("posEnd: %.1f, speedScan:%.1f, span: %.1f\n", endPoint, speedScan, timeSpanScan);
				timeBegin = timeCurrent;
				ROS_INFO(WHITE_BOLD "Start scanning LON. dir. in range [%.1f-->%.1f]mm with speed [%.1f]mm/s." NONE, \
					startPointLon, endPointLon, speedScan);
				if(locaType != LOCA_layByAngle)
					ROS_INFO(WHITE_BOLD "Start scanning LAT. dir. in range [%.1f-->%.1f]mm with speed [%.1f]mm/s." NONE, \
						startPointLat, endPointLat, speedScan);

				ROS_INFO("(1) Power on the related axises.");
				lzm.PowerOnAxis(AXIS_scannerLon);
				if(locaType != LOCA_layByAngle)
					lzm.PowerOnAxis(AXIS_scannerLat);
				lzm.MsSleep(100);
				ROS_INFO("(2) Initialize start positions of measuring module.");
				lzm.ControlAxis(AXIS_scannerLon, true, speedMove, startPointLon);
				if(locaType != LOCA_layByAngle)
					lzm.ControlAxis(AXIS_scannerLat, true, speedMove, startPointLat);
				ROS_INFO("(3) Check start positions of measuring module...");
				flagArrivalLon = false;
				flagArrivalLat = false;
				flagExecSteps[CMD_scanLoca] = SET_STEP1;
			}
			else if(flagExecSteps[CMD_scanLoca] == SET_STEP1)
			{
				float posNowLon = lzm.GetAxisPosition(AXIS_scannerLon);
				float posNowLat = lzm.GetAxisPosition(AXIS_scannerLat);
				if((!flagArrivalLon) && (fabs(startPointLon - posNowLon) <= 1.0))
				{
					flagArrivalLon = true;
					printf(GREEN "Lon scanner arrives init. position." ENDL);
				}
				if((locaType != LOCA_layByAngle) && (!flagArrivalLat) && (fabs(startPointLat - posNowLat) <= 1.0))
				{
					flagArrivalLat = true;
					printf(GREEN "Lat scanner arrives init. position." ENDL);
				}
				if((flagArrivalLon && flagArrivalLat) || ((locaType == LOCA_layByAngle) && (flagArrivalLon)))
				{
					ROS_INFO("(4) Arrives start position(s) of rail(s).");
					ROS_INFO("(5) Check whether current heights are in the measuring ranges.");
					bool flagException = false;
					if((rs.sensor.scannerFL <= mc.GetScanRangeMin()) || (rs.sensor.scannerFL >= mc.GetScanRangeMax()) || (rs.sensor.scannerFR <= mc.GetScanRangeMin()) || (rs.sensor.scannerFR >= mc.GetScanRangeMax()))
					{
						flagException = true;
						ROS_ERROR("Front left sensor's height[%.1f] or right one [%.1f] is out of range! Abort.", rs.sensor.scannerFL, rs.sensor.scannerFR);
					}
					if((locaType == LOCA_layByLeft) || (locaType == LOCA_fetch))
					{
						if((rs.sensor.scannerLeft <= mc.GetScanRangeMin()) || (rs.sensor.scannerLeft >= mc.GetScanRangeMax()))
						{
							flagException = true;
							ROS_ERROR("Lat. Left [%.1f] sensors' heights are out of range! Abort.", rs.sensor.scannerLeft);
						}
					}
					if((locaType == LOCA_layByRight) || (locaType == LOCA_fetch))
					{
						if((rs.sensor.scannerRight <= mc.GetScanRangeMin()) || (rs.sensor.scannerRight >= mc.GetScanRangeMax()))
						{
							flagException = true;
							ROS_ERROR("Lat. right [%.1f] sensors' heights are out of range! Abort.", rs.sensor.scannerRight);
						}
					}
					if(locaType == LOCA_fetch)
					{
						if((rs.sensor.scannerRC <= mc.GetScanRangeMin()) || (rs.sensor.scannerRC >= mc.GetScanRangeMax()))
						{
							flagException = true;
							ROS_ERROR("Lon. rear [%.1f] sensors' heights are out of range! Abort.", rs.sensor.scannerRC);
						}
					}
					if(flagException)
					{
						flagExec[CMD_scanLoca] = EXEC_OFF;
						flagExecSteps[CMD_scanLoca] = SET_INIT;
						PublishResponse(CMD_scanLoca, REPLY_failed);
						lzm.ControlAxis(AXIS_scannerLon, true, 0.0, 0.0);
						lzm.ControlAxis(AXIS_scannerLat, true, 0.0, 0.0);
						lzm.PowerOffAxis(AXIS_scannerLon);
						lzm.PowerOffAxis(AXIS_scannerLat);
					}
					else
					{
						ROS_INFO("(6) Scan to gather height data along the line(s)...");
						lzm.ControlAxis(AXIS_scannerLon, true, speedScan, endPointLon);
						heightsFL.clear();
						heightsFR.clear();
						heightsRC.clear();
						if(locaType != LOCA_layByAngle)
						{
							lzm.ControlAxis(AXIS_scannerLat, true, speedScan, endPointLat);
							heightsL.clear();
							heightsR.clear();
						}
						flagExecSteps[CMD_scanLoca] = SET_STEP2;
						timeBegin = timeCurrent;
						flagArrivalLon = false;
						flagArrivalLat = false;
						//--- initialize fucntions
						mc.CheckFullCoverDuringScanFetch();
						mc.CheckFullCoverDuringScanLay();
					}
				}
				else if(timeCurrent - timeBegin > timeSpanInit)
				{
					ROS_ERROR("Timeout during going back to inside end with time span [%.3f]s! Abort.", timeSpanInit);
					flagExec[CMD_scanLoca] = EXEC_OFF;
					flagExecSteps[CMD_scanLoca] = SET_INIT;
					lzm.ControlAxis(AXIS_scannerLon, true, 0.0, 0.0);
					lzm.ControlAxis(AXIS_scannerLat, true, 0.0, 0.0);
					PublishResponse(CMD_scanLoca, REPLY_failed);
				}
			}
			else if(flagExecSteps[CMD_scanLoca] == SET_STEP2)
			{
				bool flagException = false;
				float posNowLon = lzm.GetAxisPosition(AXIS_scannerLon);
				float posNowLat = lzm.GetAxisPosition(AXIS_scannerLat);
				// printf("lon: %.1fmm; height FL/FR/RC: %.1f,%.1f,%.1f  lat: %.1fmm; height L/R: %.1f,%.1f\n", \
				// 	posNowLon, rs.sensor.scannerFL, rs.sensor.scannerFR, rs.sensor.scannerRC, posNowLat, rs.sensor.scannerLeft, rs.sensor.scannerRight);
				if(fabs(endPointLon - posNowLon) > 1.0)
				{
					heightsFL.push_back(make_pair(rs.sensor.scannerFL, posNowLon));
					heightsFR.push_back(make_pair(rs.sensor.scannerFR, posNowLon));
					heightsRC.push_back(make_pair(rs.sensor.scannerRC, posNowLon));
				}
				else if(!flagArrivalLon)
				{
					printf(GREEN "Lon scanner arrives end position." ENDL);
					flagArrivalLon = true;
				}
				if(locaType != LOCA_layByAngle)
				{
					if(fabs(endPointLat - posNowLat) > 1.0)
					{
						heightsL.push_back(make_pair(rs.sensor.scannerLeft,  posNowLat));
						heightsR.push_back(make_pair(rs.sensor.scannerRight, posNowLat));
					}
					else if(!flagArrivalLat)
					{
						printf(GREEN "Lat scanner arrives end position." ENDL);
						flagArrivalLat = true;
					}
				}
				bool ret = false;
				if(locaType == LOCA_fetch)
					ret = mc.CheckFullCoverDuringScanFetch(rs.sensor.scannerFL, rs.sensor.scannerFR, rs.sensor.scannerRC, rs.sensor.scannerLeft, rs.sensor.scannerRight);
				else if(locaType == LOCA_layByLeft)
					ret = mc.CheckFullCoverDuringScanLay(rs.sensor.scannerFL, rs.sensor.scannerFR, rs.sensor.scannerLeft);
				else if(locaType == LOCA_layByRight)
					ret = mc.CheckFullCoverDuringScanLay(rs.sensor.scannerFL, rs.sensor.scannerFR, rs.sensor.scannerRight);
				else if(locaType == LOCA_layByAngle)
					ret = mc.CheckFullCoverDuringScanLay(rs.sensor.scannerFL, rs.sensor.scannerFR);
				else
				{
					ROS_ERROR("Illegal locaType [%d]. It should be 1(layA)/2(layL)/3(layR)/4(fetch).", locaType);
					ret = true;
				}
				if(ret)
				{
					ROS_INFO(GREEN_BOLD "All scanners have covered their edges. Break the scan for faster positioning." NONE);
				}

				if(timeCurrent - timeBegin > timeSpanScan)
				{
					ROS_ERROR("Timeout during going back to outside end with time span [%.3f]s! Abort.", timeSpanScan);
					flagExec[CMD_scanLoca] = EXEC_OFF;
					flagExecSteps[CMD_scanLoca] = SET_INIT;
					lzm.ControlAxis(AXIS_scannerLon, true, 0.0, 0.0);
					lzm.ControlAxis(AXIS_scannerLat, true, 0.0, 0.0);
					PublishResponse(CMD_scanLoca, REPLY_failed);
				}
				
				if(ret || ((flagArrivalLon && flagArrivalLat) || ((locaType == LOCA_layByAngle) && (flagArrivalLon))))
				{
					ROS_INFO("(7) Covers all edges or Arrives end position(s) of rail(s).");
					ROS_INFO("(8) Move scanners back to start points of rails.(ignore)");
					// lzm.ControlAxis(AXIS_scannerLon, true, speedMove, startPointLon);
					// lzm.ControlAxis(AXIS_scannerLat, true, speedMove, startPointLat);
					ROS_INFO("(9) Calculate angle & position results.");
					float da, dx, dy, length = 0.0, width = 0.0, toEdgeLon = 0.0, toEdgeLat = 0.0;
					bool ret;
					if(locaType == LOCA_layByAngle)
						ret = mc.ScanPositioningLay(heightsFL, heightsFR, heightsL, heightsR, &da, &dx, &dy);
					else if(locaType == LOCA_layByLeft)
					{
						heightsR.clear();
						ret = mc.ScanPositioningLay(heightsFL, heightsFR, heightsL, heightsR, &da, &dx, &dy);
					}
					else if(locaType == LOCA_layByRight)
					{
						heightsL.clear();
						ret = mc.ScanPositioningLay(heightsFL, heightsFR, heightsL, heightsR, &da, &dx, &dy);
					}
					else if(locaType == LOCA_fetch)
						ret = mc.ScanPositioningFetch(heightsFL, heightsFR, heightsRC, heightsL, heightsR, &da, &dx, &dy, &length, &width, &toEdgeLon, &toEdgeLat);
					else
					{
						ROS_ERROR("Illegal locaType [%d]. (It should be 1-layByAngle,2-layByLeft,3-layByRight,4-fetch)" ENDL, locaType);
						ret = false;
					}

					if(!ret)
					{
						PublishResponse(CMD_scanLoca, REPLY_failed);
					}
					else
					{
						ROS_INFO(GREEN_BOLD "(10){end} Calculation completed with time consumption [%.3f]s." NONE, timeCurrent - timeBegin);
						PublishResponse(CMD_scanLoca, REPLY_succeeded, da*100, dx*100, dy*100, 
							length*100, width*100, toEdgeLon*100, toEdgeLat*100, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
					}
					double timeSpanScanLon = fabs(startPointLon - endPointLon) / speedMove;
					double timeSpanScanLat = fabs(startPointLat - endPointLat) / speedMove;
					timeSpanScan = max(timeSpanScanLon, timeSpanScanLat) + 3;
					timeBegin = timeCurrent;
					flagArrivalLon = false;
					flagArrivalLat = false;
					flagExecSteps[CMD_scanLoca] = SET_STEP3;

					//--- power off axises
					flagExec[CMD_scanLoca] = EXEC_OFF;
					flagExecSteps[CMD_scanLoca] = SET_INIT;
					lzm.ControlAxis(AXIS_scannerLon, true, 0.0, 0.0);
					lzm.ControlAxis(AXIS_scannerLat, true, 0.0, 0.0);
					lzm.PowerOffAxis(AXIS_scannerLon);
					lzm.PowerOffAxis(AXIS_scannerLat);
				}
			}
			else if(flagExecSteps[CMD_scanLoca] == SET_STEP3)
			{
				float posNowLon = lzm.GetAxisPosition(AXIS_scannerLon);
				float posNowLat = lzm.GetAxisPosition(AXIS_scannerLat);
				if((!flagArrivalLon) && (fabs(startPointLon - posNowLon) <= 1.0))
				{
					flagArrivalLon = true;
					printf(GREEN "Lon scanner arrives start position." ENDL);
				}
				if((!flagArrivalLat) && (fabs(startPointLat - posNowLat) <= 1.0))
				{
					flagArrivalLat = true;
					printf(GREEN "Lat scanner arrives start position." ENDL);
				}
				if((flagArrivalLon && flagArrivalLat))
				{
					flagExec[CMD_scanLoca] = EXEC_OFF;
					flagExecSteps[CMD_scanLoca] = SET_INIT;
					lzm.ControlAxis(AXIS_scannerLon, true, 0.0, 0.0);
					lzm.ControlAxis(AXIS_scannerLat, true, 0.0, 0.0);
					lzm.PowerOffAxis(AXIS_scannerLon);
					lzm.PowerOffAxis(AXIS_scannerLat);
					ROS_INFO(GREEN "(11){post} Both scanners reach start points." NONE);
				}
				else if(timeCurrent - timeBegin > timeSpanScan)
				{
					flagExec[CMD_scanLoca] = EXEC_OFF;
					flagExecSteps[CMD_scanLoca] = SET_INIT;
					lzm.ControlAxis(AXIS_scannerLon, true, 0.0, 0.0);
					lzm.ControlAxis(AXIS_scannerLat, true, 0.0, 0.0);
					lzm.PowerOffAxis(AXIS_scannerLon);
					lzm.PowerOffAxis(AXIS_scannerLat);
					ROS_ERROR("(11){post} Timeout while waiting scanners reach start points! Quit.");
				}
			}
		}
		else
		{
			flagExec[CMD_scanLoca] = EXEC_OFF;
			flagExecSteps[CMD_scanLoca] = SET_INIT;
			lzm.ControlAxis(AXIS_scannerLon, true, 0.0, 0.0);
			lzm.ControlAxis(AXIS_scannerLat, true, 0.0, 0.0);
			lzm.PowerOffAxis(AXIS_scannerLon);
			lzm.PowerOffAxis(AXIS_scannerLat);
		}
	}
	else if(flagExec[CMD_scanLoca2])
	{
		static double timeStart;
		static int    type, detectPoint;
		static float  tileLenLon, tileLenLat;
		if(cmdArray[CMD_scanLoca2][0])
		{
			if(flagExecSteps[CMD_scanLoca2] == SET_INIT)
			{
				timeStart = timeCurrent;
				type = cmdArray[CMD_scanLoca2][1]; // 1/11-fetch; 2-lay; 3-acq. most out z heights; 4-acq. middle z heights; 5-acq. most in z heights
				detectPoint = cmdArray[CMD_scanLoca2][2]; // only for mode 4
				float tileLon = 0, tileLat = 0;
				if(type == 1)
				{
					tileLon = cmdArray[CMD_scanLoca2][3];
					tileLat = cmdArray[CMD_scanLoca2][4];
					mc.LoadTileSize(tileLon, tileLat);
				}
				ROS_INFO(CYAN "Trigger scanner positioning 2 with type: [%d] with detect point [%d], tile lon/lat [%.0f/%.0f]mm" NONE,
				 	type, detectPoint, tileLon, tileLat);
				ROS_INFO("(1-fetch using 3 cams; 11-fetch using 5 cams; 2-lay; 3-get most out heights; 4-get middle heights; 5-get most in heights.)");
				PublishScannerCmd(1);	// 1-send for once; 2-shut down
				flagExecSteps[CMD_scanLoca2] = SET_STEP1;
			}
			else if(flagExecSteps[CMD_scanLoca2] == SET_STEP1)
			{
				if(timeCurrent - timeStart > 2)
				{
					ROS_ERROR("Failed to get response of scanner data! Abort.");
					PublishResponse(CMD_scanLoca2, REPLY_failed);
					flagExecSteps[CMD_scanLoca2] = SET_INIT;
					flagExec[CMD_scanLoca2] = EXEC_OFF;
				}
				if(flagRecScanner)
				{
					flagExecSteps[CMD_scanLoca2] = SET_STEP2;
				}
			}
			else if(flagExecSteps[CMD_scanLoca2] == SET_STEP2)
			{
				if(type == 1 || type == 11)
				{
					float dx, dy, da, pitch, roll, dz;
					bool flag = mc.ScannerPositioningFetch(	type,
															scanner.frontleft,
															scanner.frontright,
															scanner.rearcenter,
															scanner.left,
															scanner.right,
															&dx, &dy, &da,
															&pitch, &roll, &dz);
					if(flag)
					{
						mc.GetTileSize(&tileLenLon, &tileLenLat);
						PublishResponse(CMD_scanLoca2, REPLY_succeeded, da*100, dx*100, dy*100, 
							tileLenLat*100, tileLenLon*100, pitch*100, roll*100, dz*100, 0.0, 0.0, 0.0, 0.0, 0.0);
					}
					else
					{
						PublishResponse(CMD_scanLoca2, REPLY_failed);
					}
				}
				else if(type == 2)
				{
					float dx, dy, da;
					bool flag = mc.ScannerPositioningLay(	scanner.frontleft,
															scanner.frontright,
															scanner.right,
															&dx, &dy, &da);
					if(flag)
					{
						PublishResponse(CMD_scanLoca2, REPLY_succeeded, da*100, dx*100, dy*100,
							tileLenLat*100, tileLenLon*100, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
					}
					else
					{
						PublishResponse(CMD_scanLoca2, REPLY_failed);
					}
				}
				else if(type == 3)
				{
					float zFrontleft, zFrontright, zRight;
					bool flag = mc.ScannerZaxisHeights(	scanner.frontleft,
														scanner.frontright,
														scanner.right,
														&zFrontleft, &zFrontright, &zRight);
					if(flag)
					{
						PublishResponse(CMD_scanLoca2, REPLY_succeeded, 0.0, 0.0, 0.0, 
							tileLenLat*100, tileLenLon*100, 0.0, 0.0, 0.0,zFrontleft*100, zFrontright*100, zRight*100, 0.0, 0.0);
					}
					else
					{
						PublishResponse(CMD_scanLoca2, REPLY_failed);
					}
				}
				else if(type == 4)
				{
					float zFrontleft, zFrontright;
					bool flag = mc.ScannerZaxisHeightsMiddle(	scanner.frontleft,
																scanner.frontright,
																detectPoint,
																&zFrontleft, &zFrontright);
					if(flag)
					{
						PublishResponse(CMD_scanLoca2, REPLY_succeeded, 0.0, 0.0, 0.0, 
							tileLenLat*100, tileLenLon*100, 0.0, 0.0, 0.0, zFrontleft*100, zFrontright*100, 0.0, 0.0, 0.0);
					}
					else
					{
						PublishResponse(CMD_scanLoca2, REPLY_failed);
					}
				}
				else if(type == 5)
				{
					float zFrontleft, zFrontright, zRear, zLeft, zRight;
					bool flag = mc.ScannerZaxisHeightsMostIn(	scanner.frontleft,
																scanner.frontright,
																scanner.rearcenter,
																scanner.left,
																scanner.right,
																&zFrontleft, &zFrontright, &zRear, &zLeft, &zRight);
					if(flag)
					{
						PublishResponse(CMD_scanLoca2, REPLY_succeeded, 0.0, 0.0, 0.0, 
							tileLenLat*100, tileLenLon*100, 0.0, 0.0, 0.0, zFrontleft*100, zFrontright*100, zRight*100, zLeft*100, zRear*100);
					}
					else
					{
						PublishResponse(CMD_scanLoca2, REPLY_failed);
					}
				}
				else if(type == 6)
				{
					float pitch, roll;
					bool flag = mc.ScannerTiltAdj(	scanner.frontleft,
													scanner.frontright,
													&pitch, &roll);
					if(flag)
					{
						PublishResponse(CMD_scanLoca2, REPLY_succeeded, 0.0, 0.0, 0.0, 0.0, 0.0, pitch*100, roll*100, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
					}
					else
					{
						PublishResponse(CMD_scanLoca2, REPLY_failed);
					}
				}
				else
				{
					ROS_ERROR("[scan] Mismathed type [%d]. It must be 1(fetch) or 2(lay) or 3(get last z heights) or 4(get middle z heights) or 5(get most in heights) or 6(calc tilt angles)", type);
				}
				flagExecSteps[CMD_scanLoca2] = SET_INIT;
				flagExec[CMD_scanLoca2] = EXEC_OFF;
			}
		}
	}

	//--- make a 90Hz / 9 = 10Hz calling loop hereafter
	static int loopcnt = 0;
	if(++loopcnt < 9)
		return;
	loopcnt = 0;

	//========================================== 10 Hz LOOP HEREAFTER =======================================

	static CTRL ctrl;	// defined in "motion_control.hpp"
	static bool flagFirstTrigger = true;
	if(timeCurrent - timeRecXbox < 1.0)
	{
		if(!flagFirstTrigger)
		{
			flagFirstTrigger = true;
			ROS_WARN("=== Remote control is ON ===");
		}
		if(xbox.buttonLB) //[20231030] stop all when no reception of xbox data for 1s
		{
			// ROS_INFO("ctrl");
			mc.ManipCtrl(xbox, ctrl);
		}
		else
		{
			// ROS_INFO("stop");
			mc.StopAll(ctrl);
		}

		// execute motion control
		lzm.ControlBaseMove(&ctrl.arrayVel[AXIS_moveFL]);
#ifdef BLR4
		lzm.ControlBaseSupport(&ctrl.arrayVel[AXIS_supportFL], &ctrl.arrayPos[AXIS_supportFL]);
#endif
		lzm.ControlBaseSteer(&ctrl.arrayVel[AXIS_steerFL], &ctrl.arrayPos[AXIS_steerFL]);
	}
	else if(flagFirstTrigger)
	{
		// ROS_INFO("loose");
		flagFirstTrigger = false;
		ROS_WARN("=== Remote control is OFF ===");
		// mc.StopAll(ctrl);
		// lzm.StopBaseMove();
		// lzm.StopBaseSteer();
		// lzm.StopBaseSupport();
	}

	if(flagExec[CMD_moveScanner])
	{
		if(cmdArray[CMD_moveScanner][0])
		{
			static double timeBegin, timeSpan, timePoweroff;
			static float posBeginLon, posBeginLat;
			static float posGoalLon, posGoalLat;
			static float velLon, velLat;
			static float posLon, posLat;
			static bool  mode, flagLon, flagLat;
			if(flagExecSteps[CMD_moveScanner] == SET_INIT)
			{
				timeBegin = timeCurrent;
				mode   = cmdArray[CMD_moveScanner][1];
				velLon = cmdArray[CMD_moveScanner][2];
				velLat = cmdArray[CMD_moveScanner][3];
				posLon = cmdArray[CMD_moveScanner][4];
				posLat = cmdArray[CMD_moveScanner][5];
				lzm.PowerOnAxis(AXIS_scannerLon);
				lzm.PowerOnAxis(AXIS_scannerLat);
				posBeginLon = lzm.GetAxisPosition(AXIS_scannerLon);
				posBeginLat = lzm.GetAxisPosition(AXIS_scannerLat);
				double timeSpanLon, timeSpanLat;
				float velLonCompute = (velLon <= 0.1) ? 100000.0:velLon;
				float velLatCompute = (velLat <= 0.1) ? 100000.0:velLat;
				if(mode == 1)
				{
					timeSpanLon = abs(posLon - posBeginLon) / velLonCompute;
					timeSpanLat = abs(posLat - posBeginLat ) / velLatCompute;
					posGoalLon  = posLon;
					posGoalLat  = posLat;
				}
				else
				{
					timeSpanLon = abs(posLon / velLonCompute);
					timeSpanLat = abs(posLat / velLatCompute);
					posGoalLon  = posLon + posBeginLon;
					posGoalLat  = posLat  + posBeginLat;
				}
				timeSpan = max(timeSpanLon, timeSpanLat) + 10;
				ROS_INFO(CYAN "Trigger scanner trans: mod: [%d] Lon: vel[%.0f]mm/s pos[%.0f]mm Lat: vel[%.0f]mm/s pos[%.0f]mm with time span[%.0f]ms" NONE, \
					mode, velLon, posLon, velLat, posLat, timeSpan*1000.0);
				flagExecSteps[CMD_moveScanner] = SET_STEP1;
			}
			else if(flagExecSteps[CMD_moveScanner] == SET_STEP1)
			{
				if(timeCurrent - timeBegin > 0.5)
				{
					lzm.ControlAxis(AXIS_scannerLon, mode, velLon, posLon);
					lzm.ControlAxis(AXIS_scannerLat,  mode, velLat,  posLat);
					flagExecSteps[CMD_moveScanner] = SET_STEP2;
				}
			}
			else if(flagExecSteps[CMD_moveScanner] == SET_STEP2)
			{
				float posLon = lzm.GetAxisPosition(AXIS_scannerLon);
				float posLat = lzm.GetAxisPosition(AXIS_scannerLat);
				ROS_INFO_THROTTLE(5,"While moving, positions of scanner Lon [%.1f]mm Lat [%.1f]mm", posLon, posLat);
				if((fabs(posLon - posGoalLon) < 2) || (velLon < 0.1))
					flagLon = true;
				if((fabs(posLat - posGoalLat) < 2) || (velLat < 0.1))
					flagLat = true;
				if(flagLon && flagLat)
				{
					flagLon = false;
					flagLat  = false;
					ROS_INFO(GREEN "Scanner lon&lat translational movement arrival!" NONE);
					ROS_INFO("Wait 0s to power off so that scanners can reach goal...");
					timePoweroff = timeCurrent;
					flagExecSteps[CMD_moveScanner] = SET_STEP3;
				}
			}
			else if(flagExecSteps[CMD_moveScanner] == SET_STEP3)
			{
				if(timeCurrent - timePoweroff > 0)
				{
					ROS_INFO("Power off axis scannerLon and scannerLat.");
					lzm.PowerOffAxis(AXIS_scannerLon);
					lzm.PowerOffAxis(AXIS_scannerLat);
					flagExec[CMD_moveScanner] = EXEC_OFF;
					flagExecSteps[CMD_moveScanner] = SET_INIT;
					PublishResponse(CMD_moveScanner, REPLY_succeeded);
				}
			}
			if(timeCurrent - timeBegin > timeSpan)
			{
				ROS_ERROR("Scanner lon&lat translational movement timeout!");
				flagExec[CMD_moveScanner] = EXEC_OFF;
				flagExecSteps[CMD_moveScanner] = SET_INIT;
				lzm.PowerOffAxis(AXIS_scannerLon);
				lzm.PowerOffAxis(AXIS_scannerLat);
				if(fabs(lzm.GetAxisPosition(AXIS_scannerLon) - posBeginLon) < 1)
					PublishError(ERR_nomoveScanLon);
				if(fabs(lzm.GetAxisPosition(AXIS_scannerLat) - posBeginLat) < 1)
					PublishError(ERR_nomoveScanLat);
			}
		}
		else
		{
			flagExec[CMD_moveScanner] = EXEC_OFF;
			flagExecSteps[CMD_moveScanner] = SET_INIT;
			lzm.PowerOffAxis(AXIS_scannerLon);
			lzm.PowerOffAxis(AXIS_scannerLat);
		}
	}

	if(flagExec[CMD_allStop])
	{
		flagExec[CMD_allStop] = EXEC_OFF;
		lzm.StopBaseMove();
		lzm.StopBaseSteer();
#ifdef BLR4
		lzm.StopBaseSupport();
#endif
		for(int axis=AXIS_paverLift; axis<AXIS_endEntity; axis++)
			lzm.ControlAxis(axis, true, 0, 0);
		lzm.StopSingleAxisPulse(AXIS_deliverL);
		lzm.StopSingleAxisPulse(AXIS_deliverM);
		lzm.StopSingleAxisPulse(AXIS_deliverR);
		flagExec[CMD_scanLoca] = EXEC_OFF;
		flagExecSteps[CMD_scanLoca] = SET_INIT;
	}
	if(flagExec[CMD_help])
	{
		flagExec[CMD_help] = EXEC_OFF;
		int type = cmdArray[CMD_help][0];
		if(type == 1)
			PrintHelpOnScreen();
		else
			lzm.PrintAxisOnScreen();
	}

	//--- 机械臂关节控制 (迁移到ZMotion)
	if(flagExec[CMD_armJoint])
	{
		flagExec[CMD_armJoint] = EXEC_OFF;
		int mode = cmdArray[CMD_armJoint][0];  // 0-增量, 1-绝对
		int vel = cmdArray[CMD_armJoint][1];   // 速度
		float j1 = cmdArray[CMD_armJoint][2] / 1000.0;  // 关节1角度(度)
		float j2 = cmdArray[CMD_armJoint][3];           // 关节2位置(mm)
		float j3 = cmdArray[CMD_armJoint][4];           // 关节3位置(mm)
		float j4 = cmdArray[CMD_armJoint][5] / 1000.0;  // 关节4角度(度)

		ROS_INFO("ARM Joint Control: mode[%d] vel[%d] j1[%.2f] j2[%.1f] j3[%.1f] j4[%.2f]",
				mode, vel, j1, j2, j3, j4);

		// 控制各个关节轴
		lzm.ControlAxis(AXIS_armRotate1, true, vel, j1 * 1000);  // 转换为毫度
		lzm.ControlAxis(AXIS_armLift, true, vel, j2);
		lzm.ControlAxis(AXIS_armTrans, true, vel, j3);
		lzm.ControlAxis(AXIS_armRotate2, true, vel, j4 * 1000);  // 转换为毫度

		PublishResponse(CMD_armJoint, REPLY_succeeded);
	}

	//--- 机械臂直线控制 (迁移到ZMotion)
	if(flagExec[CMD_armLinear])
	{
		flagExec[CMD_armLinear] = EXEC_OFF;
		int mode = cmdArray[CMD_armLinear][0];  // 0-增量, 1-绝对
		int vel = cmdArray[CMD_armLinear][1];   // 速度
		float dx = cmdArray[CMD_armLinear][2];  // X方向(mm)
		float dy = cmdArray[CMD_armLinear][3];  // Y方向(mm)
		float dz = cmdArray[CMD_armLinear][4];  // Z方向(mm)
		float da = cmdArray[CMD_armLinear][5] / 1000.0;  // 角度(度)

		ROS_INFO("ARM Linear Control: mode[%d] vel[%d] dx[%.1f] dy[%.1f] dz[%.1f] da[%.2f]",
				mode, vel, dx, dy, dz, da);

		// 这里需要实现直线运动的逆运动学计算
		// 暂时简化处理，后续需要完善
		PublishResponse(CMD_armLinear, REPLY_succeeded);
	}

	//--- 机械臂停止
	if(flagExec[CMD_armStop])
	{
		flagExec[CMD_armStop] = EXEC_OFF;
		ROS_INFO("ARM Stop Command");

		// 停止所有机械臂轴
		lzm.ControlAxis(AXIS_armRotate1, true, 0, 0);
		lzm.ControlAxis(AXIS_armLift, true, 0, 0);
		lzm.ControlAxis(AXIS_armTrans, true, 0, 0);
		lzm.ControlAxis(AXIS_armRotate2, true, 0, 0);

		PublishResponse(CMD_armStop, REPLY_succeeded);
	}

	if(flagExec[CMD_controlAxis])
	{
		if(cmdArray[CMD_controlAxis][0])
		{
			static int axis;
			static double timeBegin, timeSpan;
			static float posBegin, posCmd;
			static bool ctrlMode, flagArrival;
			if(flagExecSteps[CMD_controlAxis] == SET_INIT)
			{
				axis = cmdArray[CMD_controlAxis][1];
				ROS_INFO("Power on the axis [%d] for first and delay 0.5s.", axis);
				timeBegin = timeCurrent;
				lzm.PowerOnAxis(axis);
				posBegin = lzm.GetAxisPosition(axis);
				flagExecSteps[CMD_controlAxis] = SET_STEP1;
				timeSpan = 5;
			}
			else if(flagExecSteps[CMD_controlAxis] == SET_STEP1)
			{
				if(timeCurrent - timeBegin > 0.5)
				{
					bool mode = cmdArray[CMD_controlAxis][2];
					float vel = cmdArray[CMD_controlAxis][3];
					float pos = cmdArray[CMD_controlAxis][4];
					lzm.ControlAxis(axis, mode, vel ,pos);
					flagExecSteps[CMD_controlAxis] = SET_STEP2;
					ROS_INFO(CYAN "Get command of controling axis[%d](%s) with mode[%d] vel[%.0f] pos[%.0f]",\
						axis, lzm.TransEnumToStringAxis(axis), mode, vel, pos);
					posCmd   = pos;
					ctrlMode = mode;
					flagArrival = false;
					if(mode == 0)
						timeSpan = fabs(pos / vel) + 5;
					else
						timeSpan = fabs(posBegin - posCmd) / vel + 5;
				}
			}
			else if(flagExecSteps[CMD_controlAxis] == SET_STEP2)
			{
				float pos = lzm.GetAxisPosition(axis);
				printf("mode: %d, posBegin: %.0f, posCmd: %.0f, posNow: %.0f\n", ctrlMode, posBegin, posCmd, pos);
				if(ctrlMode == 0)
				{
					if(fabs(pos - (posBegin + posCmd)) < 0.5)
						flagArrival = true;
				}
				else
				{
					if(fabs(pos - posCmd) < 0.5)
						flagArrival = true;
				}
				if(flagArrival)
				{
					flagArrival = false;
					ROS_INFO(GREEN "General control of axis movement arrival!" NONE);
					// ROS_INFO("Power off the axis [%d]", axis);
					// lzm.PowerOffAxis(axis);
					lzm.SingleCancel(cmdArray[CMD_controlAxis][1]);
					flagExec[CMD_controlAxis] = EXEC_OFF;
					flagExecSteps[CMD_controlAxis] = SET_INIT;
					PublishResponse(CMD_controlAxis, REPLY_succeeded);
				}
			}
			if(timeCurrent - timeBegin > timeSpan)
			{
				ROS_ERROR("Axis [%d] movement timeout!", axis);
				flagExec[CMD_controlAxis] = EXEC_OFF;
				flagExecSteps[CMD_controlAxis] = SET_INIT;
				// lzm.PowerOffAxis(AXIS_workendLat);
				lzm.SingleCancel(cmdArray[CMD_controlAxis][1]);
			}
		}
		else
		{
			flagExec[CMD_controlAxis] = EXEC_OFF;
			flagExecSteps[CMD_controlAxis] = SET_INIT;
			lzm.ControlAxis(cmdArray[CMD_controlAxis][1], 1, 0.0, 0.0);
			lzm.SingleCancel(cmdArray[CMD_controlAxis][1]);
			// lzm.PowerOffAxis(cmdArray[CMD_controlAxis][1]);
		}
	}

#ifdef BLR4
	if(flagExec[CMD_supportLift])
	{
		if(cmdArray[CMD_supportLift][0])    // state
		{
			static float goalPos[4];
			if(flagExecSteps[CMD_supportLift] == SET_INIT)
			{
				flagExecSteps[CMD_supportLift] = SET_LOOP;
				float arrayVel[4], arrayPos[4];
				for(int index=0; index<4; index++)
				{
					arrayVel[index] = cmdArray[CMD_supportLift][index+1];
					arrayPos[index] = cmdArray[CMD_supportLift][index+5];
					goalPos[index] = arrayPos[index];
				}
				if((fabs(arrayVel[0])>10) || (fabs(arrayVel[1])>10) || (fabs(arrayVel[2])>10) || (fabs(arrayVel[3])>10))
				{
					flagExec[CMD_supportLift] = EXEC_OFF;
					flagExecSteps[CMD_supportLift] = SET_INIT;
					PublishResponse(CMD_supportLift, REPLY_rejected);
					ROS_WARN("Illegal speed value [%.0f,%.0f,%.0f,%.0f]mm/s received and rejected to execute: too fast!", \
						arrayVel[0], arrayVel[1], arrayVel[2], arrayVel[3]);
				}
				else
				{
					lzm.ControlBaseSupport(arrayVel, arrayPos);
					//ROS_WARN("test: arrayVel %.2f, arrayPos%.2f", arrayVel[0], arrayPos[0]);
				}
			}
			else
			{
				bool ret = lzm.CheckArrivalSupportDist(goalPos, 2);
				if(ret)
				{
					flagExec[CMD_supportLift] = EXEC_OFF;
					flagExecSteps[CMD_supportLift] = SET_INIT;
					PublishResponse(CMD_supportLift, REPLY_succeeded);
					ROS_INFO(GREEN "Support arrival." NONE);
				}
				// TODO: time due return failure
			}
		}
		else
		{
			flagExec[CMD_supportLift] = EXEC_OFF;
			flagExecSteps[CMD_supportLift] = SET_INIT;
			lzm.StopBaseSupport();
		}
	}
	if(flagExec[CMD_supportLeveling])
	{
		static float threshTorque, toleranceAngle, arrayGoal[4];
		static bool flagArrive, flagCheck;
		static double timeBegin, timeCtrlEnd;
		if(cmdArray[CMD_supportLeveling][0])    // state
		{
			if(flagExecSteps[CMD_supportLeveling] == SET_INIT) // receive parameters
			{
				if(rs.sensor.imuBaseRoll == 0 && rs.sensor.imuBasePitch == 0)
				{
					flagExecSteps[CMD_supportLeveling] = SET_INIT;
					flagExec[CMD_supportLeveling] = EXEC_OFF;
					PublishResponse(CMD_supportLeveling, REPLY_failed);
					ROS_ERROR("IMU value [%.3f,%.3f] is abnormal! Quit suport  leveling.", rs.sensor.imuBaseRoll, rs.sensor.imuBasePitch);
				}
				else
				{
					ROS_WARN("Start supporters leveling control...");
					timeBegin = timeCurrent;
					arrayGoal[0]   = cmdArray[CMD_supportLeveling][1];
					threshTorque   = cmdArray[CMD_supportLeveling][2];
					toleranceAngle = cmdArray[CMD_supportLeveling][3];
					arrayGoal[0] = (arrayGoal[0] > 10) ? 10:arrayGoal[0];

					flagExecSteps[CMD_supportLeveling] = SET_STEP1;
					arrayGoal[1] = arrayGoal[0];
					arrayGoal[2] = arrayGoal[0];
					arrayGoal[3] = arrayGoal[0];
					float velArray[4] = {3, 3, 3, 3};
					lzm.ControlBaseSupport(velArray, arrayGoal);
					ROS_WARN("[1] On the way to init. positions...");
				}
			}
			else if(flagExecSteps[CMD_supportLeveling] == SET_STEP1) // check init. pos. arrival and lift all 4 legs up
			{
				if(lzm.CheckArrivalSupportDist(arrayGoal, 2))
				{
					//--- lift 4 legs up a further small dist. such that all torques can be negative values
					//--- torques are positive when motor rotates to descend legs; and vice versa.
					ROS_WARN("[2] Supporters arrives init. positions; And lift up 4 legs a further dist...");
					arrayGoal[0] += 4; 
					arrayGoal[1] += 4; 
					arrayGoal[2] += 4; 
					arrayGoal[3] += 4; 
					float velArray[4] = {1, 1, 1, 1};
					lzm.ControlBaseSupport(velArray, arrayGoal);
					flagArrive = false;
					flagExecSteps[CMD_supportLeveling] = SET_STEP2;
				}
			}
			else if(flagExecSteps[CMD_supportLeveling] == SET_STEP2) // check pos. arrival
			{
				if((lzm.CheckArrivalSupportDist(arrayGoal, 2)) && (flagArrive == false))
				{
					//--- lift 4 legs up a further small dist. such that all torques can be negative values
					//--- torques are positive when motor rotates to descend legs; and vice versa.
					ROS_WARN("[3] Lift up 4 legs a further dist arrival. And await 3s for stable torque...");
					flagArrive = true;
					timeCtrlEnd = timeCurrent;
				}
				//--- await 3s to get stable state such that torques of 4 legs can be stable
				if(flagArrive == true)
				{
					if(timeCurrent - timeCtrlEnd > 3)
					{
						ROS_WARN("[3(2)] 3s time due, and starts torque avaraging...");
						flagExecSteps[CMD_supportLeveling] = SET_STEP3;
					}
				}
			}
			else if(flagExecSteps[CMD_supportLeveling] == SET_STEP3) // avarage torques of four supporters
			{
				float posArray[4];
				posArray[0] = lzm.GetAxisPosition(AXIS_supportFL);
				posArray[1] = lzm.GetAxisPosition(AXIS_supportFR);
				posArray[2] = lzm.GetAxisPosition(AXIS_supportRL);
				posArray[3] = lzm.GetAxisPosition(AXIS_supportRR);
				int torArray[4];
				torArray[0] = lzm.GetAxisTorque(AXIS_supportFL);
				torArray[1] = lzm.GetAxisTorque(AXIS_supportFR);
				torArray[2] = lzm.GetAxisTorque(AXIS_supportRL);
				torArray[3] = lzm.GetAxisTorque(AXIS_supportRR);
				//--- time-delay control
				if(flagArrive)
				{
					if(mc.SupportTorqueAvaraging(posArray, torArray, threshTorque, ctrl))
					{
						ROS_WARN("[4] Torques all meets threshold. And starts base leveling...");
						flagExecSteps[CMD_supportLeveling] = SET_STEP4;
					}
					lzm.ControlBaseSupport(&ctrl.arrayVel[AXIS_supportFL], &ctrl.arrayPos[AXIS_supportFL]);
					flagArrive = false;
					flagCheck  = true;
				}
				if(flagCheck && lzm.CheckArrivalSupportDist(&ctrl.arrayPos[AXIS_supportFL], 1))
				{
					timeCtrlEnd = timeCurrent;
					flagCheck = false;
				}
				if(timeCurrent - timeCtrlEnd > 3.0)
				{
					flagArrive = true;
				}
			}
			else if(flagExecSteps[CMD_supportLeveling] == SET_STEP4) // pose leveling control
			{
				float posArray[4];
				posArray[0] = lzm.GetAxisPosition(AXIS_supportFL);
				posArray[1] = lzm.GetAxisPosition(AXIS_supportFR);
				posArray[2] = lzm.GetAxisPosition(AXIS_supportRL);
				posArray[3] = lzm.GetAxisPosition(AXIS_supportRR);
				if(mc.SupportLeveling(rs.sensor.imuBasePitch, rs.sensor.imuBaseRoll, posArray, toleranceAngle, ctrl))
				{
					flagExec[CMD_supportLeveling] = EXEC_OFF;
					flagExecSteps[CMD_supportLeveling] = SET_INIT;
					lzm.StopBaseSupport();
					PublishResponse(CMD_supportLeveling, REPLY_succeeded);
					double timeConsume = timeCurrent - timeBegin;
					ROS_INFO("[5] Base leveling finished with time consumption: %.3f s", timeConsume);
				}
				lzm.ControlBaseSupport(&ctrl.arrayVel[AXIS_supportFL], &ctrl.arrayPos[AXIS_supportFL]);
			}
		}
		else
		{
			flagExec[CMD_supportLeveling] = EXEC_OFF;
			flagExecSteps[CMD_supportLeveling] = SET_INIT;
			lzm.StopBaseSupport();
		}
	}
#endif
	if(flagExec[CMD_steerTrans])
	{
		if(cmdArray[CMD_steerTrans][0])
		{
			static float arrayPosGoal[4];
			if(flagExecSteps[CMD_steerTrans] == SET_INIT)
			{
				flagExecSteps[CMD_steerTrans] = SET_LOOP;
				for(int axis=AXIS_steerFL; axis<=AXIS_steerRR; axis++)
				{
					unsigned int index = axis - AXIS_steerFL;
					ctrl.arrayVel[axis] = cmdArray[CMD_steerTrans][index+1];
					ctrl.arrayPos[axis] = cmdArray[CMD_steerTrans][index+5];
					arrayPosGoal[index] = ctrl.arrayPos[axis];
				}

				float arrayPosNow[4], arrayVelDiff[4];
				for(int axis=AXIS_steerFL; axis<=AXIS_steerRR; axis++)
					arrayPosNow[axis-AXIS_steerFL] = lzm.GetAxisPosition(axis);
				mc.JointSteerAndMove(arrayPosNow, ctrl, arrayVelDiff, 1000);	//unit: mdeg
				lzm.ControlBaseMove(arrayVelDiff);
				lzm.ControlBaseSteer(&ctrl.arrayVel[AXIS_steerFL], &ctrl.arrayPos[AXIS_steerFL]);
				ROS_INFO("steer vel[%.4f] move vel[%.4f]", ctrl.arrayVel[AXIS_steerFL], arrayVelDiff[0]);
			}
			else
			{
				bool ret = lzm.CheckArrivalSteer(arrayPosGoal, 0.1);
				if(ret)
				{
					flagExec[CMD_steerTrans] = EXEC_OFF;
					flagExecSteps[CMD_steerTrans] = SET_INIT;
					lzm.StopBaseMove();
					lzm.StopBaseSteer();
					PublishResponse(CMD_steerTrans, REPLY_succeeded);
					ROS_INFO(GREEN "Steering angle arrival." NONE);
				}
			}
		}
		else
		{
			lzm.StopBaseMove();
			lzm.StopBaseSteer();
			flagExec[CMD_steerTrans] = EXEC_OFF;
			flagExecSteps[CMD_steerTrans] = SET_INIT;
		}
	}
	if(flagExec[CMD_steerTurn])
	{
		flagExec[CMD_steerTurn] = EXEC_OFF;
		if(cmdArray[CMD_steerTurn][0])
		{
			float arrayVel[4], arrayPos[4];
			float omega = cmdArray[CMD_steerTurn][1];
			float angle = cmdArray[CMD_steerTurn][2] / 57.29578;
			mc.KinematicsSpeed(angle, 0, ctrl);
			for(unsigned char i=AXIS_steerFL; i<=AXIS_steerRR; i++)
			{
				arrayPos[i] = ctrl.arrayPos[i] * 57.29578;
				arrayVel[i] = omega;
			}
			lzm.ControlBaseSteer(arrayVel, arrayPos);
			//ROS_INFO("vel[%.3f] pos[%.3f] Steer wheels for turning head executed.", arrayVel[0], arrayPos[0]);
			PublishResponse(CMD_steerTurn, REPLY_finished);
		}
		else
		{
			lzm.StopBaseSteer();
		}
	}
	if(flagExec[CMD_spin])
	{
		if(cmdArray[CMD_spin][0])
		{
			static float steerGoal[4], lineDist[4];
			if(flagExecSteps[CMD_spin] == SET_INIT)
			{
				float omegaSteer = 150000;	// modified as a constant value to turn wheels
				float arrayVel[4], arrayPos[4];
				mc.ComputeOnsituSteerAngle(ctrl);
				for(unsigned char i=0; i<4; i++)
				{
					arrayVel[i] = omegaSteer;
					arrayPos[i] = ctrl.arrayPos[i+AXIS_steerFL] * 57295.78;
					steerGoal[i] = arrayPos[i];
				}
				lzm.ControlBaseSteer(arrayVel, arrayPos);
				flagExecSteps[CMD_spin] = SET_STEP1;
				ROS_INFO("[A] Executing steering to goal angle of four wheels.");
			}
			else if(flagExecSteps[CMD_spin] == SET_STEP1)
			{
				if(lzm.CheckArrivalSteer(steerGoal, 10))
				{
					flagExecSteps[CMD_spin] = SET_STEP2;
					ROS_INFO("[B] Goal steering angle arrival, ready to make spin move.");
				}
			}
			else if(flagExecSteps[CMD_spin] == SET_STEP2)
			{
				float spinOmega  = cmdArray[CMD_spin][1];
				float angleBase  = cmdArray[CMD_spin][2];
				float lineSpeed[4] = {0,0,0,0};
				mc.ComputeOnsituLineSpeed(spinOmega/50.0, lineSpeed);	// to fit omega scalar
				lzm.ControlBaseMove(lineSpeed);
				mc.ComputeOnsituLineDist(angleBase, lineDist);
				lzm.CheckStatusSpin(SET_INIT, lineDist);
				flagExecSteps[CMD_spin] = SET_STEP3;
				ROS_WARN("linespeed[%.3f,%.3f,%.3f,%.3f] spinOmega[%.3f] lineDist[%.3f,%.3f,%.3f,%.3f]", \
							lineSpeed[0], lineSpeed[1],lineSpeed[2],lineSpeed[3],spinOmega, \
							lineDist[0], lineDist[1],lineDist[2],lineDist[3]);
				ROS_INFO("[C] Executing spin move.");
			}
			else if(flagExecSteps[CMD_spin] == SET_STEP3)
			{
				if(lzm.CheckStatusSpin(SET_LOOP, lineDist))
				{
					flagExec[CMD_spin] = EXEC_OFF;
					flagExecSteps[CMD_spin] = SET_INIT;
					lzm.StopBaseSteer();
					lzm.StopBaseMove();
					PublishResponse(CMD_spin, REPLY_succeeded);
					ROS_INFO(GREEN "[D] Destiny base orientation arrival. Finish spin move." NONE);
				}
				// TODO: add time out and reply and reset flags
			}
		}
		else
		{
			flagExec[CMD_spin] = EXEC_OFF;
			flagExecSteps[CMD_spin] = SET_INIT;
			lzm.StopBaseSteer();
			lzm.StopBaseMove();
		}
	}

	if(flagExec[CMD_moveLat])
	{
		if(cmdArray[CMD_moveLat][0])
		{
			float arrayVel[4], arrayPos[4];
			static float arrayPosGoal[4];
			if(flagExecSteps[CMD_moveLat] == SET_INIT)
			{
				flagExecSteps[CMD_moveLat] = SET_LOOP;
				for(int axis=AXIS_steerFL; axis<=AXIS_steerRR; axis++)
				{
					unsigned int index = axis - AXIS_steerFL;
					ctrl.arrayVel[axis] = fabs(cmdArray[CMD_moveLat][1]);
					if(axis == AXIS_steerFL)
					{
						ctrl.arrayPos[axis] = fabs(cmdArray[CMD_moveLat][2]);
					}
					else if(axis == AXIS_steerFR)
					{
						ctrl.arrayPos[axis] = -( fabs(cmdArray[CMD_moveLat][2]));
					}
					else if(axis == AXIS_steerRL)
					{
						ctrl.arrayPos[axis] = -( fabs(cmdArray[CMD_moveLat][2]));
					}
					else if(axis == AXIS_steerRR)
					{
						ctrl.arrayPos[axis] = fabs(cmdArray[CMD_moveLat][2]);
					}
					arrayPosGoal[index] = ctrl.arrayPos[axis];
				}
				lzm.ControlBaseSteer(&ctrl.arrayVel[AXIS_steerFL], &ctrl.arrayPos[AXIS_steerFL]);
				ROS_INFO("steer vel[%.1f] steer Pos[%.1f]", ctrl.arrayVel[AXIS_steerFL], ctrl.arrayPos[AXIS_steerFL]);
			}
			else
			{
				bool ret = lzm.CheckArrivalSteer(arrayPosGoal, 10);
				if(ret)
				{
					flagExec[CMD_moveLat] = EXEC_OFF;
					flagExecSteps[CMD_moveLat] = SET_INIT;
					lzm.StopBaseMove();
					lzm.StopBaseSteer();
					PublishResponse(CMD_moveLat, REPLY_succeeded);
					ROS_INFO("Steering angle arrival.");
				}
			}
		}
		else
		{
			lzm.StopBaseMove();
			lzm.StopBaseSteer();
			flagExec[CMD_moveLat] = EXEC_OFF;
			flagExecSteps[CMD_moveLat] = SET_INIT;
		}
	}

	if(flagExec[CMD_clearAlarm])
	{
		if(cmdArray[CMD_clearAlarm][0])
		{
			unsigned char num = cmdArray[CMD_clearAlarm][1];
			if(num == 0) // clear all axises
			{
				for(unsigned char axis=0; axis<AXIS_endEntity; axis++)
				{
					lzm.ClearAlarms(axis);
					ROS_INFO("Clear alarm and reset axis [%d].", axis);
					lzm.MsSleep(100);
				}
			}
			else
			{
				for(unsigned char idx=0; idx<num; idx++)
				{
					lzm.ClearAlarms(cmdArray[CMD_clearAlarm][2+idx]);
					ROS_INFO("Clear alarm and reset axis [%d].", cmdArray[CMD_clearAlarm][2+idx]);
					lzm.MsSleep(100);
				}
			}
		}
		flagExec[CMD_clearAlarm] = EXEC_OFF;
	}

	if(flagExec[CMD_wheelRoll])
	{
		if(cmdArray[CMD_wheelRoll][0])      // state
		{
			static float distance, posWheel[4];
			float arrayVel[4], speed;
			if(flagExecSteps[CMD_wheelRoll] == SET_INIT)
			{
				flagExecSteps[CMD_wheelRoll] = SET_LOOP;
				posWheel[0] = lzm.GetAxisPosition(AXIS_moveFL);
				posWheel[1] = lzm.GetAxisPosition(AXIS_moveFR);
				posWheel[2] = lzm.GetAxisPosition(AXIS_moveRL);
				posWheel[3] = lzm.GetAxisPosition(AXIS_moveRR);
				speed    = cmdArray[CMD_wheelRoll][1];		// unit: mm/s
				distance = cmdArray[CMD_wheelRoll][2];    	// distance of tracking, unit:mm
				for(unsigned char i=0; i<4; i++)
				{
					arrayVel[i] = speed;
				}
				lzm.ControlBaseMove(arrayVel);
			}
			else
			{
				int ret = lzm.CheckArrivalTrackDist(posWheel, distance);
				if(ret)    // task succeeded
				{
					flagExec[CMD_wheelRoll] = EXEC_OFF;
					flagExecSteps[CMD_wheelRoll] = SET_INIT;
					PublishResponse(CMD_wheelRoll, REPLY_succeeded);
					lzm.StopBaseMove();
					ROS_INFO(GREEN "Roll wheels arrival." NONE);
				}
			}
		}
		else
		{
			flagExec[CMD_wheelRoll] = EXEC_OFF;
			flagExecSteps[CMD_wheelRoll] = SET_INIT;
			lzm.StopBaseMove();
		}
	}
	if(flagExec[CMD_trackLine])
	{
		static float speed;
		static short distStart, distance;
		static bool mode; // true:laser distance based stepping back; false: motor encoder based stepping back
		if(cmdArray[CMD_trackLine][0])      // state
		{
			static float posWheel[4];
			if(flagExecSteps[CMD_trackLine] == SET_INIT)
			{
				if(rs.sensor.psdFront>19.0 || rs.sensor.psdFront<-19.0 || rs.sensor.psdRear>19.0 || rs.sensor.psdRear<-19.0)
				{
					flagExec[CMD_trackLine] = EXEC_OFF;
					ROS_WARN("Laser line is not placed in the middle(+-19.0mm) of PSD(actual:%.1f,%.1f)! Quit tracking.", rs.sensor.psdFront, rs.sensor.psdRear);
					PublishResponse(CMD_trackLine, REPLY_rejected);
					PublishError(ERR_psdTrackNotCenter);
					lzm.StopBaseMove();
					lzm.StopBaseSteer();
				}
				else
				{
					flagExecSteps[CMD_trackLine] = SET_LOOP;
					mode     = cmdArray[CMD_trackLine][1];
					speed    = cmdArray[CMD_trackLine][2];		// unit: mm/s
					distance = cmdArray[CMD_trackLine][3];    	// distance of tracking, unit:mm
					ROS_INFO(CYAN "Got cmd of track line: mod[%d](0-check laser;1-check encoder) vel[%.1f]mm/s dis[%d]mm" NONE, mode, speed, distance);
					posWheel[0] = lzm.GetAxisPosition(AXIS_moveFL);
					posWheel[1] = lzm.GetAxisPosition(AXIS_moveFR);
					posWheel[2] = lzm.GetAxisPosition(AXIS_moveRL);
					posWheel[3] = lzm.GetAxisPosition(AXIS_moveRR);
					if(!mode)
					{
						distStart = rs.sensor.laserDistance;
						ROS_INFO("Get current laser module measured distance value: [%d]mm. dist: [%d]mm", distStart, distance);
						if((distStart > 15000) || (distStart<10))
						{
							flagExec[CMD_trackLine] = EXEC_OFF;
							flagExecSteps[CMD_trackLine] = SET_INIT;
							PublishResponse(CMD_trackLine, REPLY_failed);
							PublishError(ERR_laserDist);
							lzm.StopBaseMove();
							lzm.StopBaseSteer();
							ROS_WARN("laser distance value [%.0f] is out of range! Quit tracking.", rs.sensor.laserDistance);
						}
					}
				}
			}
			else
			{
				if(rs.sensor.psdFront>35.0 || rs.sensor.psdFront<-35.0 || rs.sensor.psdRear>35.0 || rs.sensor.psdRear<-35.0)
				{
					flagExec[CMD_trackLine] = EXEC_OFF;
					flagExecSteps[CMD_trackLine] = SET_INIT;
					PublishResponse(CMD_trackLine, REPLY_failed);
					if((rs.sensor.psdFront > 35.0) || (rs.sensor.psdFront < -35.0)) PublishError(ERR_psdFront);
					if((rs.sensor.psdRear  > 35.0) || (rs.sensor.psdRear  < -35.0)) PublishError(ERR_psdRear);
					lzm.StopBaseMove();
					lzm.StopBaseSteer();
					ROS_WARN("PSD value [%.1f,%.1f] is out of range! Quit tracking.", rs.sensor.psdFront, rs.sensor.psdRear);
				}
				else
				{
					float arrayVel[4], arrayPos[4], arrayVelMove[4];
					mc.LineTracker(rs.sensor.psdFront, rs.sensor.psdRear, speed, ctrl);
					for(unsigned char i=0; i<4; i++)
					{
						arrayVel[i] = ctrl.arrayVel[i+AXIS_steerFL];
						arrayPos[i] = ctrl.arrayPos[i+AXIS_steerFL];
						arrayVelMove[i] = ctrl.arrayVel[i+AXIS_moveFL];
					}
					lzm.ControlBaseMove(arrayVelMove);
					lzm.ControlBaseSteer(arrayVel, arrayPos);

					bool flagArrival = false;
					if(!mode) // laser distance detection
					{
						ushort offset = 0;
						if(speed > 0.0)
						{
							if(rs.sensor.laserDistance >= distStart + distance - offset)
							{
								printf("Forward tracking arrival by laser detection.\n");
								flagArrival = true;
							}
						}
						else
						{
							if(rs.sensor.laserDistance <= distStart - distance + offset)
							{
								printf("Reversed tracking arrival by laser detection.\n");
								flagArrival = true;
							}
							//printf("vel: %.3f m/s, laser now: %d mm, all: %d: disstart: %d mm, distance: %d mm, offset: %d mm\n",speed, rs.sensor.laserDistance,distStart - distance + offset, distStart,distance,offset);
						}
					}
					else
					{
						if(lzm.CheckArrivalTrackDist(posWheel, distance))
							flagArrival = true;
					}
					if(flagArrival)    // task succeeded
					{
						flagExec[CMD_trackLine] = EXEC_OFF;
						flagExecSteps[CMD_trackLine] = SET_INIT;
						PublishResponse(CMD_trackLine, REPLY_succeeded);
						lzm.StopBaseMove();
						lzm.StopBaseSteer();
						if(!mode)
							ROS_INFO(GREEN "Tracking arrival with distance increment right:[%.0f]mm." NONE, rs.sensor.laserDistance-distStart);
						else
							ROS_INFO(GREEN "Tracking arrival with motor encoder incremental check." NONE);
					}
				}
			}
		}
		else
		{
			flagExec[CMD_trackLine] = EXEC_OFF;
			flagExecSteps[CMD_trackLine] = SET_INIT;
			lzm.StopBaseMove();
			lzm.StopBaseSteer();
		}
	}

	//---------------- Paver module controls as following -------------

	if(flagExec[CMD_paverLift])
	{
		static double timeBegin, timeSpan;
		if(cmdArray[CMD_paverLift][0])
		{
			if(flagExecSteps[CMD_paverLift] == SET_INIT)
			{
				flagExecSteps[CMD_paverLift] = SET_LOOP;
				unsigned int mode = cmdArray[CMD_paverLift][1];
				float vel = cmdArray[CMD_paverLift][2];
				float pos = cmdArray[CMD_paverLift][3];
				// lzm.ControlPaver(AXIS_paverLift, vel, pos, mode);
				lzm.ControlAxis(AXIS_paverLift, mode, vel, pos);
				timeBegin = timeCurrent;

				axisPosArray[AXIS_paverLift] = lzm.GetAxisPosition(AXIS_paverLift);
				timeSpan = pos / vel + 5;
			}
			else
			{
				//TODO: acquire position of reaching out, feed back when arrival
				if(timeCurrent - timeBegin > timeSpan)
				{
					flagExec[CMD_paverLift] = EXEC_OFF;
					flagExecSteps[CMD_paverLift] = SET_INIT;
					PublishResponse(CMD_paverLift, REPLY_timedue);
					if((fabs(lzm.GetAxisPosition(AXIS_paverLift) - axisPosArray[AXIS_paverLift])) < 2)
						PublishError(ERR_nomovePaverLift);
					ROS_WARN("time due.");
					lzm.SingleCancel(AXIS_paverLift);
					// lzm.PowerOffAxis(AXIS_paverLift);
				}
			}
		}
		else
		{
			flagExec[CMD_paverLift] = EXEC_OFF;
			flagExecSteps[CMD_paverLift] = SET_INIT;
			lzm.SingleCancel(AXIS_paverLift);
			// lzm.PowerOffAxis(AXIS_paverLift);
		}
	}
	if(flagExec[CMD_paverRoll])
	{
		static double timeBegin, timeSpan;
		if(cmdArray[CMD_paverRoll][0])
		{
			if(flagExecSteps[CMD_paverRoll] == SET_INIT)
			{
				flagExecSteps[CMD_paverRoll] = SET_LOOP;
				unsigned int mode = cmdArray[CMD_paverRoll][1];
				float vel  = cmdArray[CMD_paverRoll][2];
				float pos  = cmdArray[CMD_paverRoll][3];
				// lzm.ControlPaver(AXIS_paverRoll, vel, pos, mode);
				lzm.ControlAxis(AXIS_paverRoll, mode, vel, pos);
				timeBegin = timeCurrent;

				axisPosArray[AXIS_paverRoll] = lzm.GetAxisPosition(AXIS_paverRoll);
				timeSpan = pos / vel + 5;
			}
			else
			{
				//TODO: acquire position of reaching out, feed back when arrival
				if(timeCurrent - timeBegin > timeSpan)
				{
					flagExec[CMD_paverRoll] = EXEC_OFF;
					flagExecSteps[CMD_paverRoll] = SET_INIT;
					PublishResponse(CMD_paverRoll, REPLY_timedue);
					if((fabs(lzm.GetAxisPosition(AXIS_paverRoll) - axisPosArray[AXIS_paverRoll])) < 20)
						PublishError(ERR_nomovePaverRoll);
					ROS_WARN("time due.");
					lzm.SingleCancel(AXIS_paverRoll);
					// lzm.PowerOffAxis(AXIS_paverRoll);
				}
			}
		}
		else
		{
			flagExec[CMD_paverRoll] = EXEC_OFF;
			flagExecSteps[CMD_paverRoll] = SET_INIT;
			lzm.SingleCancel(AXIS_paverRoll);
			// lzm.PowerOffAxis(AXIS_paverRoll);
		}
	}

//kun_test
	if(flagExec[CMD_paverDeliver])
	{
		flagExec[CMD_paverDeliver] = EXEC_OFF;
		if(cmdArray[CMD_paverDeliver][0])
		{
        
           lzm.OpenPaver(1);    
			// float speed = cmdArray[CMD_paverDeliver][1];
			// for(unsigned char axis=AXIS_deliverM; axis<=AXIS_deliverR; axis++)
			// {
				
			// 	lzm.ControlSingleAxisVel(axis, 1, speed); //-1=-, 1=+
              
			// }

			PublishResponse(CMD_paverDeliver, REPLY_finished);
		}
		else
		{
			flagExec[CMD_paverDeliver] = EXEC_OFF;
			flagExecSteps[CMD_paverDeliver] = SET_INIT;
	        lzm.OpenPaver(0);
			// for(unsigned char axis=AXIS_deliverM; axis<=AXIS_deliverR; axis++)
			// {
			// 	lzm.StopSingleAxisPulse(axis);	// stop pulse: 3
			// }
			PublishResponse(CMD_paverDeliver, REPLY_finished);
		}
	}
//kun_test
	if(flagExec[CMD_paverLeveling])
	{
		if(cmdArray[CMD_paverLeveling][0])
		{
			static bool flagFirstSuc, flagFirstCheck;
			static int  cntAbnormalData = 0;
			static double timeBegin;
			static float heightLeft, heightRight, heightTol;
			if(flagExecSteps[CMD_paverLeveling] == SET_INIT)
			{
				flagExecSteps[CMD_paverLeveling] = SET_LOOP;
				heightLeft  = cmdArray[CMD_paverLeveling][1];	// unit:mm for following 2, too
				heightRight = cmdArray[CMD_paverLeveling][2];
				heightTol   = cmdArray[CMD_paverLeveling][3];
				heightTol = mc.LimitValue(heightTol, 1, 30);
				ROS_INFO(CYAN "Got cmd of paverleveling: height L/R [%.1f, %.1f] tol [%.1f]" NONE, heightLeft, heightRight, heightTol);
					
				// if(rs.sensor.psdLeft>35.0 || rs.sensor.psdLeft<-35.0 || rs.sensor.psdRight>35.0 || rs.sensor.psdRight<-35.0)
				if(0)
				{
					ROS_ERROR("PSD value [%.1f,%.1f] is out of range! Quit paver leveling.", rs.sensor.psdLeft, rs.sensor.psdRight);
					flagExec[CMD_paverLeveling] = EXEC_OFF;
					PublishResponse(CMD_paverLeveling, REPLY_failed);
					if((rs.sensor.psdLeft  > 35.0) || (rs.sensor.psdLeft  < -35.0)) PublishError(ERR_psdLeft);
					if((rs.sensor.psdRight > 35.0) || (rs.sensor.psdRight < -35.0)) PublishError(ERR_psdRight);
				}
				else
				{
					flagFirstSuc = false;
					cntAbnormalData = 0;
					lzm.PowerOnAxis(AXIS_paverLift);
					lzm.PowerOnAxis(AXIS_paverRoll);
					lzm.MsSleep(100);

					timeBegin = timeCurrent;
					axisPosArray[AXIS_paverLift] = lzm.GetAxisPosition(AXIS_paverLift);
					axisPosArray[AXIS_paverRoll] = lzm.GetAxisPosition(AXIS_paverRoll);
					flagFirstCheck = false;
				}
			}
			else
			{
				if(rs.sensor.psdLeft>35.0 || rs.sensor.psdLeft<-35.0 || rs.sensor.psdRight>35.0 || rs.sensor.psdRight<-35.0)
				{
					ROS_WARN("Paver's PSD data [%.1f,%.1f] are out of range!", rs.sensor.psdLeft, rs.sensor.psdRight);
					if(cntAbnormalData++ >= 20)
					{
						ROS_ERROR("Paver's PSD data are out of range for continuous 5 times! Abort paver leveling.");
						flagExecSteps[CMD_paverLeveling] = SET_INIT;
						flagExec[CMD_paverLeveling] = EXEC_OFF;
						PublishResponse(CMD_paverLeveling, REPLY_failed);
						if((rs.sensor.psdLeft  > 35.0) || (rs.sensor.psdLeft  < -35.0)) PublishError(ERR_psdLeft);
						if((rs.sensor.psdRight > 35.0) || (rs.sensor.psdRight < -35.0)) PublishError(ERR_psdRight);
					}
					lzm.ControlAxis(AXIS_paverLift, 0, 0, ctrl.arrayPos[AXIS_paverLift]);
					lzm.ControlAxis(AXIS_paverRoll, 0, 0, ctrl.arrayPos[AXIS_paverRoll]);
				}
				else
				{
					cntAbnormalData = 0;
					//--- high positive for paver's two PSDs, right side of robot positive for base's two PSDs
					bool ret = mc.PaverLeveling(rs.sensor.psdLeft, rs.sensor.psdRight, heightLeft, heightRight, heightTol, ctrl);
					lzm.ControlAxis(AXIS_paverLift, 0, ctrl.arrayVel[AXIS_paverLift], ctrl.arrayPos[AXIS_paverLift]);
					lzm.ControlAxis(AXIS_paverRoll, 0, ctrl.arrayVel[AXIS_paverRoll], ctrl.arrayPos[AXIS_paverRoll]);

					if(ret)
					{
						//--- comment the following line to achieve permanent leveling
						//--- uncomment the following line to stop leveling if the tolerance is met
						// flagExecSteps[CMD_paverLeveling] = SET_INIT;
						// flagExec[CMD_paverLeveling] = EXEC_OFF;
						if(!flagFirstSuc)
						{
							flagFirstSuc = true;
							PublishResponse(CMD_paverLeveling, REPLY_succeeded);
							ROS_INFO("Send REPLY_succeeded for just once while paver leveling is succeeded.");
						}
						ROS_INFO_THROTTLE(5, GREEN "Achieve paver leveling." NONE);
					}

					if(!flagFirstCheck && (timeCurrent - timeBegin > 10))
					{
						flagFirstCheck = true;
						if(fabs(lzm.GetAxisPosition(AXIS_paverLift) - axisPosArray[AXIS_paverLift]) < 1)
							PublishError(ERR_nomovePaverLift);
						if(fabs(lzm.GetAxisPosition(AXIS_paverRoll) - axisPosArray[AXIS_paverRoll]) < 20)
							PublishError(ERR_nomovePaverRoll);
					}
				}
			}
		}
		else
		{
			flagExecSteps[CMD_paverLeveling] = SET_INIT;
			flagExec[CMD_paverLeveling] = EXEC_OFF;
			lzm.SingleCancel(AXIS_paverLift);
			lzm.SingleCancel(AXIS_paverRoll);
			PublishResponse(CMD_paverLeveling, REPLY_finished);
		}
	}

	//----------------------------- workend switcher & ADC converter -------------------

	if(flagExec[CMD_suck])
	{
		flagExec[CMD_suck] = EXEC_OFF;
		char state = cmdArray[CMD_suck][0];
		if     (state == 1)	rs.ControlPeripheralDevices("sucker", "ON");
		else if(state == 2) rs.ControlPeripheralDevices("sucker", "release");
		else if(state == 0) rs.ControlPeripheralDevices("sucker", "OFF");
		else
			printf(RED "State received out of range [%d]! No execution." ENDL, state);
		PublishResponse(CMD_suck, REPLY_finished);
	}
	if(flagExec[CMD_vibrate])
	{
		flagExec[CMD_vibrate] = EXEC_OFF;
		char state = cmdArray[CMD_vibrate][0];
		float voltage = cmdArray[CMD_vibrate][1] / 10.0;
#ifdef BLR_2
		if(state == 1)	rs.ControlPeripheralDevices("vibrator", "ON");
		else 			rs.ControlPeripheralDevices("vibrator", "OFF");
#else
		voltage = (state == 0) ? 0.0:voltage;
		lzm.DAConverter(0, voltage);
#endif
		PublishResponse(CMD_vibrate, REPLY_finished);
	}
	if(flagExec[CMD_lamp])
	{
		flagExec[CMD_lamp] = EXEC_OFF;
		char state = cmdArray[CMD_lamp][0];
		if(state == 1)	rs.ControlPeripheralDevices("lamp", "ON");
		else 			rs.ControlPeripheralDevices("lamp", "OFF");
		PublishResponse(CMD_lamp, REPLY_finished);
	}
	if(flagExec[ACQ_laserDist])
	{
		unsigned int replyName = REPLY_succeeded;
		flagExec[ACQ_laserDist] = EXEC_OFF;
		float disArray[5];
		
		disArray[0] = rs.sensor.scannerLeft;
		disArray[1] = rs.sensor.scannerRight;
		disArray[2] = rs.sensor.scannerRC;
		disArray[3] = rs.sensor.scannerFL;
		disArray[4] = rs.sensor.scannerFR;
		disArray[5] = rs.sensor.scannerHead;
		for(int i=0; i<6; i++)
		{
			if(disArray[i] == 0.0)
				replyName = REPLY_failed;
				PublishError(ERR_laserHeightLeft);
				break;
		}
		
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_laserDist);
		msgReply.data.push_back(replyName);
		for(unsigned char i=0; i<5; i++)	// front, right, rear, left, another one in sequence
		{
			//--- set 0 if value is out of range(SCAN_RANGE_MIN-130mm)
			// disArray[i] = ((disArray[i]>20.0) && (disArray[i]<150.0)) ? disArray[i]:0.0;
			msgReply.data.push_back((int)disArray[i]);
		}
		pubReply.publish(msgReply);
		ROS_INFO("Distance measured: [%.3f, %.3f,%.3f,%.3f,%.3f]mm", \
			disArray[0], disArray[1], disArray[2], disArray[3], disArray[4]);
		disArray[0] = 0.0;
		disArray[1] = 0.0;
		disArray[2] = 0.0;
		disArray[3] = 0.0;
		disArray[4] = 0.0;
		disArray[5] = 0.0;
	}
	if(flagExec[CMD_workendLeveling])
	{
		if(cmdArray[CMD_workendLeveling][0])
		{
			static double timeBegin4WorkendLeveling, timePrevCtrl = 0, timeBegin;
			static int flagEnd, flagIgnore, flagFirstCheck;
			static float tol, goalRoll, goalPitch;
			static float beginRoll, beginPitch;  // record begin values of roll and pitch so as to check if they are not changing(error)
			if(flagExecSteps[CMD_workendLeveling] == SET_INIT)
			{
				if(rs.sensor.imuWorkendRoll == 0 && rs.sensor.imuWorkendPitch == 0)
				{
					flagExecSteps[CMD_workendLeveling] = SET_INIT;
					flagExec[CMD_workendLeveling] = EXEC_OFF;
					PublishResponse(CMD_workendLeveling, REPLY_failed);
					ROS_ERROR("IMU value [%.3f,%.3f] is abnormal! Quit workend leveling.", rs.sensor.imuWorkendRoll, rs.sensor.imuWorkendPitch);
					PublishError(ERR_imuWorkendPitch);
					PublishError(ERR_imuWorkendRoll);
				}
				else
				{
					flagExecSteps[CMD_workendLeveling] = SET_STEP1;
					beginRoll  = rs.sensor.imuWorkendRoll;
					beginPitch = rs.sensor.imuWorkendPitch;

					lzm.PowerOnAxis(AXIS_workendRoll);
					lzm.PowerOnAxis(AXIS_workendPitch);
					timeBegin4WorkendLeveling = timeCurrent;
					flagIgnore = false;
				}
			}
			else if(flagExecSteps[CMD_workendLeveling] == SET_STEP1)
			{
				if(timeCurrent - timeBegin4WorkendLeveling > 0.3)	// await a stable state after powering on
				{
					flagExecSteps[CMD_workendLeveling] = SET_LOOP;
					flagEnd   = cmdArray[CMD_workendLeveling][1];  // true-one time leveling
					tol       = cmdArray[CMD_workendLeveling][2] / 1000.0;
					goalRoll  = cmdArray[CMD_workendLeveling][3] / 1000.0;
					goalPitch = cmdArray[CMD_workendLeveling][4] / 1000.0;
					tol = (tol<0.01 || tol>2000)? 2000.0:tol;
					ROS_INFO(CYAN "Start workend leveling with received param: once[%d] tol[%.3f]deg goalRoll[%.3f]deg goalPitch[%.3f]deg" NONE,\
					 	flagEnd, tol, goalRoll, goalPitch);
					if((fabs(rs.sensor.imuWorkendPitch - goalPitch) < tol) && (fabs(rs.sensor.imuWorkendRoll - goalRoll) < tol))
					{
						flagIgnore = true;
						if(flagEnd)
						{
							flagExecSteps[CMD_workendLeveling] = SET_INIT;
							flagExec[CMD_workendLeveling] = EXEC_OFF;
							PublishResponse(CMD_workendLeveling, REPLY_succeeded);
							lzm.PowerOffAxis(AXIS_workendRoll);
							lzm.PowerOffAxis(AXIS_workendPitch);
							ROS_INFO(GREEN "Current roll[%.3f]deg and pitch[%.3f]deg meet goalRoll[%.3f] goalPitch[%.3f]. Workend leveling completed. " NONE,\
								rs.sensor.imuWorkendRoll, rs.sensor.imuWorkendPitch, goalRoll, goalPitch);
						}
					}
					else
					{
						axisPosArray[AXIS_workendPitch] = lzm.GetAxisPosition(AXIS_workendPitch);
						axisPosArray[AXIS_workendRoll]  = lzm.GetAxisPosition(AXIS_workendRoll) ;
						timeBegin = timeCurrent;
						flagFirstCheck = false;
					}
				}
			}
			else
			{
				if(timeCurrent - timePrevCtrl > 0.05)
				{
					timePrevCtrl = timeCurrent;
					bool ret = mc.WorkendLeveling(rs.sensor.imuWorkendPitch, rs.sensor.imuWorkendRoll, goalRoll, goalPitch, tol, ctrl);
					lzm.ControlAxis(AXIS_workendPitch, 0, ctrl.arrayVel[AXIS_workendPitch], ctrl.arrayPos[AXIS_workendPitch]);
					lzm.ControlAxis(AXIS_workendRoll,  0, ctrl.arrayVel[AXIS_workendRoll],  ctrl.arrayPos[AXIS_workendRoll]);
					if(ret)
					{
						if((beginRoll == rs.sensor.imuWorkendRoll) && (beginPitch == rs.sensor.imuWorkendPitch) && (!flagIgnore))
						{
							flagExecSteps[CMD_workendLeveling] = SET_INIT;
							flagExec[CMD_workendLeveling] = EXEC_OFF;
							ROS_ERROR("Workend leveling failed due to no-changing imu data: roll[%.3f] pitch[%.3f]. Quit." NONE, rs.sensor.imuWorkendRoll, rs.sensor.imuWorkendPitch);
							PublishResponse(CMD_workendLeveling, REPLY_failed);
							PublishError(ERR_imuWorkendPitch);
							PublishError(ERR_imuWorkendRoll);
						}
						else
						{
							PublishResponse(CMD_workendLeveling, REPLY_succeeded);
						}
						if(flagEnd)
						{
							flagExecSteps[CMD_workendLeveling] = SET_INIT;
							flagExec[CMD_workendLeveling] = EXEC_OFF;
							lzm.ControlAxis(AXIS_workendPitch, 0, 0.0, 0.0);
							lzm.ControlAxis(AXIS_workendRoll,  0, 0.0, 0.0);
							ROS_INFO(GREEN "Workend leveling completed. Quit." NONE);
							lzm.PowerOffAxis(AXIS_workendRoll);
							lzm.PowerOffAxis(AXIS_workendPitch);
						}
						else
						{
							ROS_INFO_THROTTLE(5,GREEN "Workend leveling achieved." NONE);
						}

					}
				}
				if(!flagFirstCheck && (timeCurrent - timeBegin > 10))
				{
					flagFirstCheck = true;
					if((fabs(rs.sensor.imuWorkendPitch) > 0.1) && (fabs(lzm.GetAxisPosition(AXIS_workendPitch) - axisPosArray[AXIS_workendPitch]) < 2))
						PublishError(ERR_nomoveWorkendPitch);
					if((fabs(rs.sensor.imuWorkendRoll) > 0.1) && (fabs(lzm.GetAxisPosition(AXIS_workendRoll) - axisPosArray[AXIS_workendRoll]) < 2))
						PublishError(ERR_nomoveWorkendRoll);
				}
			}
		}
		else
		{
			flagExecSteps[CMD_workendLeveling] = SET_INIT;
			flagExec[CMD_workendLeveling] = EXEC_OFF;
			lzm.ControlAxis(AXIS_workendPitch, 0, 0.0, 0.0);
			lzm.ControlAxis(AXIS_workendRoll,  0, 0.0, 0.0);
			lzm.PowerOffAxis(AXIS_workendRoll);
			lzm.PowerOffAxis(AXIS_workendPitch);
		}
	}
	if(flagExec[CMD_workendJoints])
	{
		if(cmdArray[CMD_workendJoints][0])
		{
			static double timeBegin, timeSpan;
			static float posBeginPitch, posBeginRoll;
			static float posGoalPitch, posGoalRoll;
			static float velIncPitch, velIncRoll;
			static float posIncPitch, posIncRoll;
			static bool  mode, flagPitch, flagRoll;
			if(flagExecSteps[CMD_workendJoints] == SET_INIT)
			{
				timeBegin = timeCurrent;
				mode        = cmdArray[CMD_workendJoints][1];
				velIncPitch = cmdArray[CMD_workendJoints][2];
				velIncRoll  = cmdArray[CMD_workendJoints][3];
				posIncPitch = cmdArray[CMD_workendJoints][4];
				posIncRoll  = cmdArray[CMD_workendJoints][5];
				lzm.PowerOnAxis(AXIS_workendPitch);
				lzm.PowerOnAxis(AXIS_workendRoll);
				posBeginPitch = lzm.GetAxisPosition(AXIS_workendPitch);
				posBeginRoll  = lzm.GetAxisPosition(AXIS_workendRoll);
				double timeSpanPitch, timeSpanRoll;
				if(mode == 1)
				{
					timeSpanPitch = abs(posIncPitch - posBeginPitch) / velIncPitch;
					timeSpanRoll  = abs(posIncRoll  - posBeginRoll ) / velIncRoll;
					posGoalPitch  = posIncPitch;
					posGoalRoll   = posIncRoll;
				}
				else
				{
					timeSpanPitch = abs(posIncPitch / velIncPitch);
					timeSpanRoll  = abs(posIncRoll / velIncRoll);
					posGoalPitch  = posIncPitch + posBeginPitch;
					posGoalRoll   = posIncRoll  + posBeginRoll;
				}
				timeSpan = max(timeSpanPitch, timeSpanRoll) + 5;
				ROS_INFO(CYAN "Trigger workend rotate: mod: [%d] Pitch: vel[%.0f]mdeg/s pos[%.0f]mdeg Roll: vel[%.0f]mdeg/s pos[%.0f]mdeg with time span[%.0f]ms" NONE, \
					mode, velIncPitch, posIncPitch, velIncRoll, posIncRoll, timeSpan*1000.0);
				flagExecSteps[CMD_workendJoints] = SET_STEP1;
			}
			else if(flagExecSteps[CMD_workendJoints] == SET_STEP1)
			{
				if(timeCurrent - timeBegin > 0.5)
				{
					lzm.ControlAxis(AXIS_workendPitch, mode, velIncPitch, posIncPitch);
					lzm.ControlAxis(AXIS_workendRoll,  mode, velIncRoll,  posIncRoll);
					
					flagExecSteps[CMD_workendJoints] = SET_STEP2;
				}
			}
			else if(flagExecSteps[CMD_workendJoints] == SET_STEP2)
			{
				float posPitch = lzm.GetAxisPosition(AXIS_workendPitch);
				float posRoll  = lzm.GetAxisPosition(AXIS_workendRoll);
				ROS_INFO_THROTTLE(5,"Workend Pitch [%.1f]mdeg Roll [%.1f]mdeg", posPitch, posRoll);
				if(fabs(posPitch - posGoalPitch) < 2)
					flagPitch = true;
				if(fabs(posRoll - posGoalRoll) < 2)
					flagRoll = true;
				if(flagPitch && flagRoll)
				{
					flagPitch = false;
					flagRoll  = false;
					ROS_INFO(GREEN "Workend pitch&roll rotation arrival!" NONE);
					lzm.PowerOffAxis(AXIS_workendPitch);
					lzm.PowerOffAxis(AXIS_workendRoll);
					flagExec[CMD_workendJoints] = EXEC_OFF;
					flagExecSteps[CMD_workendJoints] = SET_INIT;
					PublishResponse(CMD_workendJoints, REPLY_succeeded);
				}
			}
			if(timeCurrent - timeBegin > timeSpan)
			{
				ROS_ERROR("Workend pitch&roll rotation timeout!");
				flagExec[CMD_workendJoints] = EXEC_OFF;
				flagExecSteps[CMD_workendJoints] = SET_INIT;
				lzm.PowerOffAxis(AXIS_workendPitch);
				lzm.PowerOffAxis(AXIS_workendRoll);
				if((fabs(rs.sensor.imuWorkendPitch) > 0.1) && (fabs(lzm.GetAxisPosition(AXIS_workendPitch) - axisPosArray[AXIS_workendPitch]) < 2))
					PublishError(ERR_nomoveWorkendPitch);
				if((fabs(rs.sensor.imuWorkendRoll) > 0.1) && (fabs(lzm.GetAxisPosition(AXIS_workendRoll) - axisPosArray[AXIS_workendRoll]) < 2))
					PublishError(ERR_nomoveWorkendRoll);
			}
		}
		else
		{
			flagExec[CMD_workendJoints] = EXEC_OFF;
			flagExecSteps[CMD_workendJoints] = SET_INIT;
			lzm.PowerOffAxis(AXIS_workendPitch);
			lzm.PowerOffAxis(AXIS_workendRoll);
		}
	}
	if(flagExec[CMD_workendAlign])
	{
		if(cmdArray[CMD_workendAlign][0])
		{
			static double timeBegin, timePrevCtrl;
			static double timeSpan;
			static int    countAlign;
			static float  tolAngle;
			if(flagExecSteps[CMD_workendAlign] == SET_INIT)
			{
				tolAngle = cmdArray[CMD_workendAlign][1];
				ROS_INFO(CYAN "[workend_align] Got command with angle tolerance: [%.0f]mdeg" NONE, tolAngle);
				lzm.PowerOnAxis(AXIS_workendRoll);
				lzm.PowerOnAxis(AXIS_workendPitch);
				timeBegin = timeCurrent;
				flagExecSteps[CMD_workendAlign] = SET_STEP1;
			}
			else if(flagExecSteps[CMD_workendAlign] == SET_STEP1)
			{
				if(timeCurrent - timeBegin > 0.3)
				{
					ROS_INFO("[workendAlign] Wait 0.3s for a stable state after powering on of axises.");
					timeSpan = 0;
					countAlign = 0;
					timePrevCtrl = 0;
					flagExecSteps[CMD_workendAlign] = SET_STEP2;
					axisPosArray[AXIS_workendPitch] = lzm.GetAxisPosition(AXIS_workendPitch);
					axisPosArray[AXIS_workendRoll]  = lzm.GetAxisPosition(AXIS_workendRoll);
				}
			}
			else if(flagExecSteps[CMD_workendAlign] == SET_STEP2)
			{
				double timeNow = timeCurrent;
				if(timeNow - timePrevCtrl > timeSpan)
				{
					if((rs.sensor.scannerFL < mc.GetScanRangeMin()) || (rs.sensor.scannerFL > mc.GetScanRangeMax()) || 
					   (rs.sensor.scannerFR < mc.GetScanRangeMin()) || (rs.sensor.scannerFR > mc.GetScanRangeMax()) || 
					   (rs.sensor.scannerRC < mc.GetScanRangeMin()) || (rs.sensor.scannerRC > mc.GetScanRangeMax())  )
					{
						ROS_ERROR("[workendAlign] Laser height data abnormal! Workend alignment aborted!");
						flagExecSteps[CMD_workendAlign] = SET_INIT;
						flagExec[CMD_workendAlign] = EXEC_OFF;
						lzm.ControlAxis(AXIS_workendRoll, 0, 0.0, 0.0);
						lzm.ControlAxis(AXIS_workendPitch, 0, 0.0, 0.0);
						lzm.PowerOffAxis(AXIS_workendRoll);
						lzm.PowerOffAxis(AXIS_workendPitch);
						PublishResponse(CMD_workendAlign, REPLY_failed);
						PublishError(ERR_laserHeightLeft);
					}
					ROS_INFO("[workendAlign] Get laser distance measured: FL/FR/Rear[%.3f, %.3f, %.3f]mm", \
						rs.sensor.scannerFL, rs.sensor.scannerFR, rs.sensor.scannerRC);

					float deltaPitch, deltaRoll;
					mc.ComputeWorkendTiltAlign(rs.sensor.scannerFL, rs.sensor.scannerFR, rs.sensor.scannerRC, &deltaPitch, &deltaRoll);

					if((fabs(deltaPitch) <= tolAngle) && (fabs(deltaRoll) <= tolAngle))
					{
						ROS_INFO(GREEN "[workendAlign] Workend alignment (pitch[%.0f]roll[%.0f]mdeg) succeeded with time span [%.1f]s!" NONE,\
							deltaPitch, deltaRoll, timeNow - timeBegin);
						flagExecSteps[CMD_workendAlign] = SET_INIT;
						flagExec[CMD_workendAlign] = EXEC_OFF;
						lzm.ControlAxis(AXIS_workendRoll, 0, 0.0, 0.0);
						lzm.ControlAxis(AXIS_workendPitch, 0, 0.0, 0.0);
						lzm.PowerOffAxis(AXIS_workendRoll);
						lzm.PowerOffAxis(AXIS_workendPitch);
						PublishResponse(CMD_workendAlign, REPLY_succeeded);
					}
					else if(timeNow - timeBegin > 30)
					{
						ROS_ERROR("[workendAlign] Timeout in parallel align! Longer than 30s.");
						flagExecSteps[CMD_workendAlign] = SET_INIT;
						flagExec[CMD_workendAlign] = EXEC_OFF;
						lzm.ControlAxis(AXIS_workendRoll, 0, 0.0, 0.0);
						lzm.ControlAxis(AXIS_workendPitch, 0, 0.0, 0.0);
						lzm.PowerOffAxis(AXIS_workendRoll);
						lzm.PowerOffAxis(AXIS_workendPitch);
						PublishResponse(CMD_workendAlign, REPLY_failed);
						if(fabs(lzm.GetAxisPosition(AXIS_workendPitch) - axisPosArray[AXIS_workendPitch]) < 2)
							PublishError(ERR_nomoveWorkendPitch);
						if(fabs(lzm.GetAxisPosition(AXIS_workendRoll) - axisPosArray[AXIS_workendRoll]) < 2)
							PublishError(ERR_nomoveWorkendRoll);
					}
					else
					{
						lzm.ControlAxis(AXIS_workendPitch, 0, 1000, -deltaPitch);
						lzm.ControlAxis(AXIS_workendRoll,  0, 1000, -deltaRoll);
						timePrevCtrl = timeNow;
						timeSpan = max(fabs(deltaPitch), fabs(deltaRoll)) / 1000.0 + 1.5;
						countAlign++;
						ROS_INFO("[workendAlign] [%d] FL/FR/Rear[%.3f,%.3f,%.3f]mm; pitchInc[%.0f]mdeg; rollInc[%.0f]mdeg", \
							countAlign, rs.sensor.scannerFL, rs.sensor.scannerFR, rs.sensor.scannerRC, deltaPitch, deltaRoll);
					}
				}
			}
		}
		else
		{
			flagExecSteps[CMD_workendAlign] = SET_INIT;
			flagExec[CMD_workendAlign] = EXEC_OFF;
			lzm.ControlAxis(AXIS_workendPitch,  0, 0.0, 0.0);
			lzm.ControlAxis(AXIS_workendRoll,   0, 0.0, 0.0);
			lzm.PowerOffAxis(AXIS_workendRoll);
			lzm.PowerOffAxis(AXIS_workendPitch);
		}
	}
	if(flagExec[CMD_workendTrans])
	{
		if(cmdArray[CMD_workendTrans][0])
		{
			static double timeBegin, timeSpan, timePoweroff;
			static float posBeginLon, posBeginLat;
			static float posGoalLon, posGoalLat;
			static float velIncLon, velIncLat;
			static float posIncLon, posIncLat;
			static bool  mode, flagLon, flagLat;
			if(flagExecSteps[CMD_workendTrans] == SET_INIT)
			{
				timeBegin = timeCurrent;
				mode      = cmdArray[CMD_workendTrans][1];
				velIncLon = cmdArray[CMD_workendTrans][2];
				velIncLat = cmdArray[CMD_workendTrans][3];
				posIncLon = cmdArray[CMD_workendTrans][4];
				posIncLat = cmdArray[CMD_workendTrans][5];
				lzm.PowerOnAxis(AXIS_workendLon);
				lzm.PowerOnAxis(AXIS_workendLat);
				posBeginLon = lzm.GetAxisPosition(AXIS_workendLon);
				posBeginLat = lzm.GetAxisPosition(AXIS_workendLat);
				double timeSpanLon, timeSpanLat;
				float velLonCompute = (velIncLon < 0.1) ? 10000.0:velIncLon;
				float velLatCompute = (velIncLat < 0.1) ? 10000.0:velIncLat;
				if(mode == 1)
				{
					timeSpanLon = abs(posIncLon - posBeginLon) / velLonCompute;
					timeSpanLat = abs(posIncLat - posBeginLat) / velLatCompute;
					posGoalLon  = posIncLon;
					posGoalLat  = posIncLat;
				}
				else
				{
					timeSpanLon = abs(posIncLon / velLonCompute);
					timeSpanLat = abs(posIncLat / velLatCompute);
					posGoalLon  = posIncLon + posBeginLon;
					posGoalLat  = posIncLat + posBeginLat;
				}
				timeSpan = max(timeSpanLon, timeSpanLat) + 5;
				ROS_INFO(CYAN "Trigger workend trans: mod: [%d] lon: vel[%.0f] pos[%.0f]mm lat: vel[%.0f] pos[%.0f]mm with time span[%.3f]s" NONE, \
					mode, velIncLon, posIncLon, velIncLat, posIncLat, timeSpan);
				flagExecSteps[CMD_workendTrans] = SET_STEP1;
			}
			else if(flagExecSteps[CMD_workendTrans] == SET_STEP1)
			{
				if(timeCurrent - timeBegin > 0.5)
				{
					lzm.ControlAxis(AXIS_workendLon, mode, velIncLon, posIncLon);
					lzm.ControlAxis(AXIS_workendLat, mode, velIncLat, posIncLat);
					flagExecSteps[CMD_workendTrans] = SET_STEP2;
				}
			}
			else if(flagExecSteps[CMD_workendTrans] == SET_STEP2)
			{
				float posLon = lzm.GetAxisPosition(AXIS_workendLon);
				float posLat = lzm.GetAxisPosition(AXIS_workendLat);
				if((fabs(posLon - posGoalLon) < 1) || (velIncLon < 0.1))
					flagLon = true;
				if((fabs(posLat - posGoalLat) < 1) || (velIncLat < 0.1))
					flagLat = true;
				if(flagLon && flagLat)
				{
					flagLon = false;
					flagLat = false;
					ROS_INFO(GREEN "Workend translational movement arrival!" NONE);
					timePoweroff = timeCurrent;
					PublishResponse(CMD_workendTrans, REPLY_succeeded);
					flagExecSteps[CMD_workendTrans] = SET_STEP3;
				}
			}
			else if(flagExecSteps[CMD_workendTrans] == SET_STEP3)
			{
				if(timeCurrent - timePoweroff > 3)
				{
					lzm.PowerOffAxis(AXIS_workendLon);
					lzm.PowerOffAxis(AXIS_workendLat);
					flagExec[CMD_workendTrans] = EXEC_OFF;
					flagExecSteps[CMD_workendTrans] = SET_INIT;
				}
			}
			if(timeCurrent - timeBegin > timeSpan)
			{
				ROS_ERROR("Workend translational movement timeout!");
				flagExec[CMD_workendTrans] = EXEC_OFF;
				flagExecSteps[CMD_workendTrans] = SET_INIT;
				lzm.PowerOffAxis(AXIS_workendLon);
				lzm.PowerOffAxis(AXIS_workendLat);
				if(!flagLon)
					PublishError(ERR_nomoveWorkendLon);
				if(!flagLat)
					PublishError(ERR_nomoveWorkendLat);
			}
		}
		else
		{
			flagExec[CMD_workendTrans] = EXEC_OFF;
			flagExecSteps[CMD_workendTrans] = SET_INIT;
			lzm.PowerOffAxis(AXIS_workendLon);
			lzm.PowerOffAxis(AXIS_workendLat);
		}
	}

	

	//------------------- acquisition as following --------------------------
	
	if(flagExec[ACQ_axisPosition])
	{
		flagExec[ACQ_axisPosition] = EXEC_OFF;
		ROS_INFO("---(You can query five axises each time in max.)");
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_axisPosition);
		msgReply.data.push_back(REPLY_finished);
		for(int idx=0; idx<5; idx++)
		{
			int axis = cmdArray[ACQ_axisPosition][idx];
			if((axis > 0) && (axis < 24))
			{
				int pos = lzm.GetAxisPosition(axis);
				msgReply.data.push_back(pos);
				ROS_INFO("Axis [%d](%s) Position[%d]", axis, lzm.TransEnumToStringAxis(axis), pos);
			}
		}
		ROS_INFO("---");
		for(int idx=0; idx<5; idx++)
			cmdArray[ACQ_axisPosition][idx] = 0;
		pubReply.publish(msgReply);
	}
	if(flagExec[ACQ_axisTorque])
	{
		flagExec[ACQ_axisTorque] = EXEC_OFF;
		ROS_INFO("---(You can query five axises each time in max.)");
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_axisTorque);
		msgReply.data.push_back(REPLY_finished);
		for(int idx=0; idx<5; idx++)
		{
			int axis = cmdArray[ACQ_axisTorque][idx];
			if((axis > 0) && (axis < 24))
			{
				int torque = lzm.GetAxisTorque(axis);
				msgReply.data.push_back(torque);
				ROS_INFO("Axis [%d](%s) Torque[%d]", axis, lzm.TransEnumToStringAxis(axis), torque);
			}
		}
		ROS_INFO("---");
		for(int idx=0; idx<5; idx++)
			cmdArray[ACQ_axisTorque][idx] = 0;
		pubReply.publish(msgReply);
	}

	if(flagExec[ACQ_pressure])
	{
		flagExec[ACQ_pressure] = EXEC_OFF;
		static float pressure = rs.sensor.airPressure;
		unsigned int replyName = (pressure > 0.1) ? REPLY_succeeded:REPLY_failed;
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_pressure);
		msgReply.data.push_back(replyName);
		msgReply.data.push_back((int)pressure);
		pubReply.publish(msgReply);
		ROS_INFO("Pressure measured: [%.0f]pascal", pressure);
		pressure = 0.0;
	}
	if(flagExec[ACQ_psd])
	{
		flagExec[ACQ_psd] = EXEC_OFF;
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_psd);
		msgReply.data.push_back(REPLY_finished);
		msgReply.data.push_back(rs.sensor.psdFront);
		msgReply.data.push_back(rs.sensor.psdRear);
		msgReply.data.push_back(rs.sensor.psdLeft);
		msgReply.data.push_back(rs.sensor.psdRight);
		pubReply.publish(msgReply);
		ROS_INFO("Psd measured: [%.1f, %.1f, %.1f, %.1f]", rs.sensor.psdFront, rs.sensor.psdRear, rs.sensor.psdLeft, rs.sensor.psdRight);
	}
	if(flagExec[ACQ_baseOri])
	{
		flagExec[ACQ_baseOri] = EXEC_OFF;
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_baseOri);
		if((rs.sensor.psdFront < -35.0) || (rs.sensor.psdFront > 35.0) || (rs.sensor.psdRear < -35.0) || (rs.sensor.psdRear > 35.0))
		{
			msgReply.data.push_back(REPLY_failed);
			ROS_WARN("Illegal PSD values, front[%.1f] rear[%.1f]", rs.sensor.psdFront, rs.sensor.psdRear);
		}
		else
		{
			float disFront = rs.sensor.psdFront / 1000.0;
			float disRear  = rs.sensor.psdRear  / 1000.0;
			float biasPos = (disFront + disRear) / 2.0;
			// float biasOri = atan2((disFront - disRear), 980.0) * 1000;
			float biasOri = atan2((disFront - disRear), 820.0) * 1000;
			msgReply.data.clear();
			msgReply.data.push_back(ACQ_baseOri);
			msgReply.data.push_back(REPLY_succeeded);
			msgReply.data.push_back(biasPos);
			msgReply.data.push_back(biasOri);
			ROS_INFO("PSD measured: F&R[%.1f, %.1f] pos[%.1f]mm ori[%.0f]mrad", rs.sensor.psdFront, rs.sensor.psdRear, biasPos, biasOri);
		}
		pubReply.publish(msgReply);
	}
	if(flagExec[ACQ_axisPosBase])
	{
		flagExec[ACQ_axisPosBase] = EXEC_OFF;
		float array[24];
		for(unsigned char axis=AXIS_moveFL; axis<=AXIS_workendPitch; axis++)
		{
			array[axis] = lzm.GetAxisPosition(axis);
		}
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_axisPosBase);
		msgReply.data.push_back(REPLY_finished);
		for(unsigned char i=AXIS_moveFL; i<=AXIS_workendPitch; i++)
			msgReply.data.push_back(array[i]);
		pubReply.publish(msgReply);

		ROS_INFO("   Move Pos: FL[%.2f] FR[%.2f] RL[%.2f] RR[%.2f]", array[0], array[1], array[2], array[3]);
		ROS_INFO("  Steer Pos: FL[%.2f] FR[%.2f] RL[%.2f] RR[%.2f]", array[4], array[5], array[6], array[7]);
#ifdef BLR4
		ROS_INFO("Support Pos: FL[%.2f] FR[%.2f] RL[%.2f] RR[%.2f]", array[8], array[9], array[10], array[11]);
#endif
		ROS_INFO("---");
		// ROS_INFO("Paver lift[%.2f] roll[%.2f] deliver[%.2f]", array[12], array[13],array[14]);
		ROS_INFO("Paver lift[%.2f] roll[%.2f] deliverM[%.2f]deliverL[%.2f]deliverR[%.2f]", array[AXIS_paverLift], array[AXIS_paverRoll],array[AXIS_deliverM],array[AXIS_deliverL],array[AXIS_deliverR]);//muxiulin
		// ROS_INFO("Workend roll[%.2f] pitch[%.2f]", array[17],array[18]); 
		ROS_INFO("Workend roll[%.2f] pitch[%.2f]", array[AXIS_workendRoll], array[AXIS_workendPitch]);
	}

	//--------------------- Main Arm Manipulation --------------------------------
	if(flagExec[CMD_connframe])
	{
		flagExec[CMD_connframe] = EXEC_OFF;
		int createOrCancel = cmdArray[CMD_connframe][0];
		int invOrFwd       = cmdArray[CMD_connframe][1];
		bool ret;
		if(createOrCancel == 1)
		{
			ret = lzm.CreateFrameKinematics(invOrFwd);
			if(invOrFwd)
				printf(CYAN "Got the cmd of creating INV-KINEMATICS of main arm." ENDL);
			else
				printf(CYAN "Got the cmd of creating FWD-KINEMATICS of main arm." ENDL);
		}
		else
		{
			lzm.CancelFrameKinematics();
			ret = true;
			if(invOrFwd)
				printf(CYAN "Got the cmd of canceling INV-KINEMATICS of main arm." ENDL);
			else
				printf(CYAN "Got the cmd of canceling FWD-KINEMATICS of main arm." ENDL);
		}
		if(ret)
			PublishResponse(CMD_connframe, REPLY_succeeded);
		else
		 	PublishResponse(CMD_connframe, REPLY_failed);
	}
	if(flagExec[CMD_mainarmOp])
	{
		flagExec[CMD_mainarmOp] = EXEC_OFF;
		int mode = cmdArray[CMD_mainarmOp][0];
		if(mode == 2)
		{
			int index = lzm.AcqCurrentIndex();
			ROS_INFO(CYAN "Got the cmd of acquiring the current waypoint index (%d)." NONE, index);
			PublishResponse(CMD_mainarmOp, REPLY_executed, index);
		}
		else
		{
			lzm.LineMoveOperation(mode);	// 0-pause; 1-resume
			if(mode == 1)
				ROS_INFO(CYAN "Got the cmd of resuming the paused main arm." NONE);
			else if (mode == 0)
				ROS_INFO(CYAN "Got the cmd of pausing the main arm." NONE);
			else
				ROS_ERROR("Got an illegal cmd with mode [%d]. (It must be 0-pause/1-resume/2-query-index)" ENDL, mode);
		}
	}
	if(flagExec[CMD_mainarmPos])
	{
		static double timeBegin;
		static int mode;
		static int num, numPrev = 0, numTotal = 0;
		static float waypoints[10][6];	// [X,Y,Rz,height,vel,mod]/[rot1, trans, rot2, height, vel, mod] unit: deg, mm and mm/s
		if(cmdArray[CMD_mainarmPos][0])
		{
			if(flagExecSteps[CMD_mainarmPos] == SET_INIT)
			{
				mode = cmdArray[CMD_mainarmPos][1];
				num  = cmdArray[CMD_mainarmPos][2];
				numPrev  = (mode == 0) ? 0:numTotal;
				numTotal = (mode == 0) ? num:numTotal+num;
				nodeAll  = numTotal;	// global variable for query
				ROS_INFO(" ");
				ROS_INFO(CYAN "=================================================================" NONE);
				ROS_INFO(CYAN "Got the cmd of controlling main arm as follows:" NONE);
				ROS_INFO(CYAN "Mode: [%d] (0-cancel existing; 1-add new to existing)" NONE, mode);
				for(int i=0; i<num; i++)
				{
					for(int j=0; j<6; j++)
						waypoints[i][j] = (float)cmdArray[CMD_mainarmPos][i*6+j+3];
					if(waypoints[i][5] == 0)
						ROS_INFO(CYAN "[Joint][%d/%d]:R1[%.0f]T[%.0f]R2[%.0f]H[%.0f]V[%.0f][0] (deg, mm)" NONE, \
							numPrev+i+1, numTotal, waypoints[i][0], waypoints[i][1], waypoints[i][2], waypoints[i][3], waypoints[i][4]);
					else if(waypoints[i][5] == 1)
						ROS_INFO(CYAN "[Line][%d/%d]:X[%.0f]Y[%.0f]Rz[%.0f]H[%.0f]V[%.0f][1] (deg, mm)" NONE, \
							numPrev+i+1, numTotal, waypoints[i][0], waypoints[i][1], waypoints[i][2], waypoints[i][3], waypoints[i][4]);
					else
					{
						ROS_ERROR("Illegal waypoint mode [%.0f]! (mode candidates are: 0-joint; 1-line)", waypoints[i][5]);
					}
				}
				ROS_INFO(CYAN "=================================================================" ENDL);

				float node[4], vel;
				node[0] = waypoints[0][0];
				node[1] = waypoints[0][1];
				node[2] = waypoints[0][2];
				node[3] = waypoints[0][3];
				vel     = waypoints[0][4];
				lzm.LineMoveInvKine(mode, node, vel); // specifically set mode for the 1st waypoints of the group
				for(int index=1; index<num; index++)
				{
					node[0] = waypoints[index][0];
					node[1] = waypoints[index][1];
					node[2] = waypoints[index][2];
					node[3] = waypoints[index][3];
					vel     = waypoints[index][4];
					lzm.LineMoveInvKine(1, node, vel);	// 1: set "add new waypoints" for the followed waypoints
				}
				// ROS_INFO("Totoal marks number after adding: [%d]" ENDL, lzm.AcqTotalMarks());
				timeBegin = timeCurrent;
				flagExecSteps[CMD_mainarmPos] = SET_LOOP;
			}
			else if(flagExecSteps[CMD_mainarmPos] == SET_LOOP)
			{
				int nodeIndex = lzm.AcqCurrentIndex();
				nodeIndex = (nodeIndex + 1) / 2;  // calling ZAux_Direct_MovePara() will add 1 in buffer
				nodeRest = (nodeIndex == 0) ? 0:(numTotal - nodeIndex + 1);	// global variable for query
				ROS_INFO_THROTTLE(5, "Current mark during continuous motion: [%d] rest[%d] All[%d]", nodeIndex, nodeRest, nodeAll);
				// ROS_INFO("Current mark during continuous motion: [%d]", nodeIndex);
				if(nodeIndex == 0)
				{
					ROS_INFO(GREEN "Succeed in finishing all %d waypoints.", nodeAll);
					lzm.ResetSequenceIndex();
					numTotal = 0;
					numPrev  = 0;
					flagExec[CMD_mainarmPos] = EXEC_OFF;
					flagExecSteps[CMD_mainarmPos] = SET_INIT;
					PublishResponse(CMD_mainarmPos, REPLY_succeeded);
				}

				if(timeCurrent - timeBegin > 200)
				{
					numTotal = 0;
					numPrev  = 0;
					flagExec[CMD_mainarmPos] = EXEC_OFF;
					flagExecSteps[CMD_mainarmPos] = SET_INIT;
					PublishResponse(CMD_mainarmPos, REPLY_timeout);
					ROS_WARN("[SHOT] Timeout in main arm control.");
				}
			}
		}
		else
		{
			flagExec[CMD_mainarmPos] = EXEC_OFF;
			flagExecSteps[CMD_mainarmPos] = SET_INIT;
			lzm.ResetSequenceIndex();
			lzm.RapidStopAll();
			numTotal = 0;
			numPrev  = 0;
		}
	}
	if(flagExec[CMD_mainarmVel])
	{
		flagExec[CMD_mainarmVel] = EXEC_OFF;
		if(cmdArray[CMD_mainarmVel][0])
		{
			int velX  = cmdArray[CMD_mainarmVel][1];	// dir.+: forward
			int velY  = cmdArray[CMD_mainarmVel][2];	// dir.+: left
			int velRz = cmdArray[CMD_mainarmVel][3];	// dir.+: up & right hand rule
			int velZ  = cmdArray[CMD_mainarmVel][4];	// dir.+: down
			ROS_INFO(CYAN "Got the cmd of controlling main arm using speed mode with speeds: X[%d]Y[%d]Rz[%d]Z[%d] mm/s mdeg/s:" NONE,
				velX, velY, velRz, velZ);
			int arrayVel[4] = {velX, velY, velRz, velZ};
			lzm.MainarmDirAndSpeedControl(arrayVel);
		}
		else
		{
			int arrayVel[4] = {0, 0, 0, 0};
			lzm.MainarmDirAndSpeedControl(arrayVel);
		}
	}
	if(flagExec[ACQ_restNodes])
	{
		flagExec[ACQ_restNodes] = EXEC_OFF;
		msgReply.data.clear();
		msgReply.data.push_back(ACQ_restNodes);
		msgReply.data.push_back(REPLY_busy);
		msgReply.data.push_back(nodeRest);
		msgReply.data.push_back(nodeAll);
		pubReply.publish(msgReply);
		ROS_INFO(CYAN "Query continuous motion status: rest[%d] all[%d]" ENDL, nodeRest, nodeAll);
	}

	//--------------------- test items --------------------------------

	//--- Add more functions or tests here
	if(flagExec[TST_scanLoca])
	{
		flagExec[TST_scanLoca] = EXEC_OFF;
		int locaType = cmdArray[ACQ_axisPosition][0];
		ROS_INFO(CYAN "Got cmd of test scan loca. by loading data from yaml with type [%d]" NONE, locaType);
		ROS_INFO("Parameter candidates are: 1-layByAngle; 2-layByLeft; 3-layByRight; 4-fetch");
		mc.LoadYamlDataForTest(locaType);
	}


}


void CallbackXbox(const sensor_msgs::Joy::ConstPtr &msg)
{
	timeRecXbox = ros::Time::now().toSec();
	xbox.buttonA	=	msg->buttons[0];
	xbox.buttonB  	=	msg->buttons[1];
	xbox.buttonX  	=	msg->buttons[2];
	xbox.buttonY  	=	msg->buttons[3];
	xbox.buttonLB 	=	msg->buttons[4];
	xbox.buttonRB 	=	msg->buttons[5];
	xbox.buttonBack =	msg->buttons[6];
	xbox.buttonStart=	msg->buttons[7];
	xbox.buttonHome	=	msg->buttons[8];
	xbox.buttonLeft	=	msg->buttons[11];	// only available for xBox
	xbox.buttonRight=	msg->buttons[12];	// only available for xBox
	xbox.buttonUp	=	msg->buttons[13];	// only available for xBox
	xbox.buttonDown	=	msg->buttons[14];	// only available for xBox
	xbox.axes1X		=	msg->axes[0];
	xbox.axes1Y		=	msg->axes[1];
	xbox.axesLT		=	msg->axes[2];
	xbox.axes2X		=	msg->axes[3];
	xbox.axes2Y		=	msg->axes[4];
	xbox.axesRT		=	msg->axes[5];
	xbox.axesButtonX=	msg->axes[6];	// compatible both to xBox and logitech
	xbox.axesButtonY=	msg->axes[7];	// compatible both to xBox and logitech
	//printf("buttons:[%d %d %d %d %d %d %d %d %d %d %d %d %d] \n",xbox.buttonA,xbox.buttonB,xbox.buttonX,xbox.buttonY,xbox.buttonLB,xbox.buttonRB,xbox.buttonBack,xbox.buttonStart,xbox.buttonHome,xbox.buttonLeft,xbox.buttonRight,xbox.buttonUp,xbox.buttonDown);
	//printf("axes:[%.8f %.8f %.8f %.8f %.8f %.8f %.1f %.1f] \n",xbox.axes1X,xbox.axes1Y,xbox.axesLT,xbox.axes2X,xbox.axes2Y,xbox.axesRT,xbox.axesButtonX,xbox.axesButtonY);
}
