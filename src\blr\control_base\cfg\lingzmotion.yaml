#=== parameters for Motion configuration ===
#--- atype: 65-EtherCAT pulse mode， 1-IO port mode
#--- accel: smaller means smoother
#--- decel: smaller means smoother
#--- sramp: bigger means slower speed change
#--- pulsePerCircle: pulse of one circle of motor axis (131072 = 2^17)
#--- reductionRatio: reduction ratio from motor axis to speed reducer axis
#--- scalar: rorate angle or move distance per circle of speed reducer output axis
#---         rotary joint:360,000 mdeg means speed control's unit is 1 mdeg/s; trans joint:N mm means 1mm/s
#---         where N is the incremental distance with unit mm when speed reducer axis rotates a circle 
#---         It must positive!
#--- limitFwd: position limit in forward direction, unit: mm
#--- limitRev: position limit in backward direction, unit: mm
#---           note that there exits a reversed installation when limitFwd is a negative value.
#--- Note: original position is set to ZERO as default
#---       forward(Fwd) means button position (NOTE: Fwd values must be greater than Rev values)
#=== Reconstructed on 2022.12.17 / 2023.07.15 for BLR#2 / 2023.09.24 for BLR#3


zmotion_ip: "************"
main_arm_control: 0 # 0-no use; 1-construct inv-kine; 2-construct fwd-kine; 3-execute just kine. init.

#----------------------------- BASE PLATFORM MODULE -------------------------------

base_move:
    atype:   65
    accel:   200
    decel:   200
    sramp:   200
    pulsePerCircle: 10000
    reductionRatio: 100
    scalar: 1099.5565  # 350pi
    limitFwd:  100000000
    limitRev: -100000000

base_steer:
    atype:   65
    accel:   12000 # 4000
    decel:   12000 # 4000
    sramp:   200
    pulsePerCircle: 10000
    reductionRatio: 90  # 调整减速比，原值100
    scalar: 324000  # 调整角度换算系数，原值360000
    limitFwd:  45000
    limitRev: -45000

#---------------------------------- PAVER MODULE ----------------------------------

paver_lift:
    atype:   65
    accel:   8000
    decel:   8000
    sramp:   100 
    pulsePerCircle: 10000
    reductionRatio: 20
    scalar: 4.0
    limitFwd:  190.0
    limitRev: -0.5

paver_roll:
    atype:   65
    accel:   8000
    decel:   8000
    sramp:   100 
    pulsePerCircle: 10000
    reductionRatio: 1812.8
    scalar: 360000.0
    limitFwd: 8000.0
    limitRev: -8000.0

#------------------------------ WORKEND LEVELING MODULE -----------------------------

workend_pitch:
    atype:   65
    accel:   2000
    decel:   2000
    sramp:   100 
    pulsePerCircle: 10000
    reductionRatio: 100
    scalar: 360000.0
    limitFwd: 5000.0  # head down when apply a positive pos.
    limitRev: -5000.0

workend_roll:
    atype:   65
    accel:   2000
    decel:   2000
    sramp:   100 
    pulsePerCircle: 10000
    reductionRatio: 100
    scalar: 360000.0
    limitFwd: 5000.0 # right side up when apply a positive pos.
    limitRev: -5000.0


#------------------------ IO motors -----------------------------

deliver_middle:
    atype:   1
    index:   1 #start from 1
    accel:   1000
    decel:   1000
    sramp:   200 
    pulsePerCircle: 120000
    reductionRatio: 10
    scalar: 360000.0
    limitFwd:  2000000000
    limitRev: -2000000000

deliver_left:
    atype:   1
    index:   2
    accel:   1000
    decel:   1000
    sramp:   200 
    pulsePerCircle: 120000
    reductionRatio: 10
    scalar: 360000.0
    limitFwd:  2000000000
    limitRev: -2000000000

deliver_right:
    atype:   1
    index:   3
    accel:   1000
    decel:   1000
    sramp:   200 
    pulsePerCircle: 120000
    reductionRatio: 10
    scalar: 360000.0
    limitFwd:  2000000000
    limitRev: -2000000000

#----------------- MAIN ARM MANIPULATION ------------------------
#--- set entity axises' units like mm and deg
#--- set virtual axises' units like mm and deg
arm_size:
   shrink_length: 600

arm_lift:
    atype:   65  # 改为EtherCAT模式，连接ZMotion
    accel:   150 # smaller means smoother  100
    decel:   100 # 100
    sramp:   200 # 200
    clutchRate: 0
    pulsePerCircle: 10000  # ZMotion标准脉冲数
    reductionRatio: 100    # 根据实际机械调整
    scalar: 24.0
    limitFwd: 1800
    limitRev: 0.0

arm_rotate1:
    atype:   65  # 改为EtherCAT模式，连接ZMotion
    accel:   10 # smaller means smoother
    decel:   10
    sramp:   200
    clutchRate: 0
    pulsePerCircle: 10000  # ZMotion标准脉冲数
    reductionRatio: 217    # 保持原减速比
    scalar: 360000         # 改为毫度单位
    limitFwd:  200000      # 改为毫度单位
    limitRev: -45000       # 改为毫度单位

arm_rotate2:
    atype:   65  # 改为EtherCAT模式，连接ZMotion
    accel:   10 # smaller means smoother 10
    decel:   10 #10
    sramp:   200
    clutchRate: 0
    pulsePerCircle: 10000      # ZMotion标准脉冲数
    reductionRatio: 100        #100
    scalar: 360000             # 改为毫度单位
    limitFwd: 180000           # 改为毫度单位
    limitRev: -180000          # 改为毫度单位

arm_trans:
    atype:   65  # 改为EtherCAT模式，连接ZMotion
    accel:   50 # smaller means smoother  #50
    decel:   50 #50
    sramp:   200
    clutchRate: 0
    pulsePerCircle: 10000  # ZMotion标准脉冲数
    reductionRatio: 100    # 根据实际机械调整
    scalar: 14.0
    limitFwd: 1200
    limitRev: 0

virtual_x:
    atype:   0
    accel:   50 # smaller means smoother
    decel:   50
    sramp:   200
    clutchRate: 0
    pulsePerCircle: 1000
    reductionRatio: 1
    scalar: 1.0
    limitFwd:  1800
    limitRev: -1800

virtual_y:
    atype:   0
    accel:   50 # smaller means smoother
    decel:   50
    sramp:   200
    clutchRate: 0
    pulsePerCircle: 1000    # unit: mm; accuracy: 0.001mm
    reductionRatio: 1
    scalar: 1.0
    limitFwd:  1500
    limitRev: -1500

virtual_rz:
    atype:   0
    accel:   50 # smaller means smoother
    decel:   50
    sramp:   200
    clutchRate: 0
    pulsePerCircle: 8388608 # use parameters of arm_rotate2 (workend rotate joint) 
    reductionRatio: 100
    scalar: 360
    limitFwd:  200
    limitRev: -45
