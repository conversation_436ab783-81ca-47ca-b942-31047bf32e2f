{"backtrace": 7, "backtraceGraph": {"commands": ["add_custom_target", "include", "find_package"], "files": ["/opt/ros/noetic/share/catkin/cmake/test/tests.cmake", "/opt/ros/noetic/share/catkin/cmake/all.cmake", "/opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 2, "file": 3, "line": 58, "parent": 0}, {"file": 2, "parent": 1}, {"command": 1, "file": 2, "line": 20, "parent": 2}, {"file": 1, "parent": 3}, {"command": 1, "file": 1, "line": 164, "parent": 4}, {"file": 0, "parent": 5}, {"command": 0, "file": 0, "line": 98, "parent": 6}]}, "id": "tests::@6890427a1f51a3e7e1df", "name": "tests", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}