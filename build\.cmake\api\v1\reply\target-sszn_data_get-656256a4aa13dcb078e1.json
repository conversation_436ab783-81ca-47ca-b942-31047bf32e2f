{"artifacts": [{"path": "devel/lib/scan_camera/sszn_data_get"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "add_definitions", "include", "find_package", "configure_shared_library_build_settings", "include_directories", "gencpp_append_include_dirs", "_generate_msg_cpp", "generate_messages"], "files": ["scan_camera/CMakeLists.txt", "/opt/ros/noetic/share/rosconsole/cmake/rosconsole-extras.cmake", "/opt/ros/noetic/share/rosconsole/cmake/rosconsoleConfig.cmake", "/opt/ros/noetic/share/roscpp/cmake/roscppConfig.cmake", "/opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake", "/opt/ros/noetic/share/catkin/cmake/tools/libraries.cmake", "/opt/ros/noetic/share/catkin/cmake/all.cmake", "CMakeLists.txt", "/opt/ros/noetic/share/gencpp/cmake/gencpp-extras.cmake", "/home/<USER>/catkin_ws/build/scan_camera/cmake/scan_camera-genmsg.cmake", "/opt/ros/noetic/share/genmsg/cmake/genmsg-extras.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 136, "parent": 0}, {"command": 1, "file": 0, "line": 127, "parent": 0}, {"command": 4, "file": 0, "line": 12, "parent": 0}, {"file": 4, "parent": 3}, {"command": 4, "file": 4, "line": 76, "parent": 4}, {"file": 3, "parent": 5}, {"command": 4, "file": 3, "line": 197, "parent": 6}, {"file": 2, "parent": 7}, {"command": 3, "file": 2, "line": 222, "parent": 8}, {"file": 1, "parent": 9}, {"command": 2, "file": 1, "line": 12, "parent": 10}, {"file": 7}, {"command": 4, "file": 7, "line": 58, "parent": 12}, {"file": 4, "parent": 13}, {"command": 3, "file": 4, "line": 20, "parent": 14}, {"file": 6, "parent": 15}, {"command": 5, "file": 6, "line": 174, "parent": 16}, {"command": 2, "file": 5, "line": 18, "parent": 17}, {"command": 2, "file": 1, "line": 8, "parent": 10}, {"command": 9, "file": 0, "line": 74, "parent": 0}, {"command": 3, "file": 10, "line": 307, "parent": 20}, {"file": 9, "parent": 21}, {"command": 8, "file": 9, "line": 31, "parent": 22}, {"command": 7, "file": 8, "line": 46, "parent": 23}, {"command": 6, "file": 8, "line": 63, "parent": 24}, {"command": 6, "file": 0, "line": 122, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " "}], "defines": [{"backtrace": 11, "define": "ROSCONSOLE_BACKEND_LOG4CXX"}, {"backtrace": 18, "define": "ROS_BUILD_SHARED_LIBS=1"}, {"backtrace": 19, "define": "ROS_PACKAGE_NAME=\"scan_camera\""}], "includes": [{"backtrace": 25, "path": "/home/<USER>/catkin_ws/build/devel/include"}, {"backtrace": 26, "path": "/home/<USER>/catkin_ws/src/scan_camera/include"}, {"backtrace": 26, "path": "/opt/ros/noetic/include"}, {"backtrace": 26, "path": "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"}], "language": "CXX", "sourceIndexes": [0]}], "id": "sszn_data_get::@5c179fccaace6ddab57a", "link": {"commandFragments": [{"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 2, "fragment": "-L/home/<USER>/catkin_ws/src/scan_camera/lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-L/home/<USER>/catkin_ws/src/scan_camera/$(catkin_LIB_DIRS)", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/home/<USER>/catkin_ws/src/scan_camera/lib:/home/<USER>/catkin_ws/src/scan_camera/$(catkin_LIB_DIRS):/opt/ros/noetic/lib", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libroscpp.so", "role": "libraries"}, {"fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.71.0", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.71.0", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librosconsole.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librosconsole_log4cxx.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librosconsole_backend_interface.so", "role": "libraries"}, {"fragment": "-llog4cxx", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libboost_regex.so.1.71.0", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libxmlrpcpp.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libroscpp_serialization.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librostime.so", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.71.0", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libcpp_common.so", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libboost_system.so.1.71.0", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4", "role": "libraries"}, {"fragment": "-lSR7Link", "role": "libraries"}], "language": "CXX"}, "name": "sszn_data_get", "nameOnDisk": "sszn_data_get", "paths": {"build": "scan_camera", "source": "scan_camera"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "scan_camera/src/sszn_data_get.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}