{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "include", "generate_messages", "add_dependencies"], "files": ["/home/<USER>/catkin_ws/build/scan_camera/cmake/scan_camera-genmsg.cmake", "/opt/ros/noetic/share/genmsg/cmake/genmsg-extras.cmake", "scan_camera/CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 2, "file": 2, "line": 74, "parent": 0}, {"command": 1, "file": 1, "line": 307, "parent": 1}, {"file": 0, "parent": 2}, {"command": 0, "file": 0, "line": 112, "parent": 3}, {"command": 3, "file": 0, "line": 226, "parent": 3}, {"command": 3, "file": 0, "line": 119, "parent": 3}]}, "dependencies": [{"backtrace": 5, "id": "std_msgs_generate_messages_lisp::@703c88a6609e6f0ac1ec"}, {"backtrace": 6, "id": "_scan_camera_generate_messages_check_deps_profiles::@5c179fccaace6ddab57a"}], "id": "scan_camera_generate_messages_lisp::@5c179fccaace6ddab57a", "name": "scan_camera_generate_messages_lisp", "paths": {"build": "scan_camera", "source": "scan_camera"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "/home/<USER>/catkin_ws/build/scan_camera/CMakeFiles/scan_camera_generate_messages_lisp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/catkin_ws/build/scan_camera/CMakeFiles/scan_camera_generate_messages_lisp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/catkin_ws/build/devel/share/common-lisp/ros/scan_camera/msg/profiles.lisp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}